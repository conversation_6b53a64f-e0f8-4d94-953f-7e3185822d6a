#!/usr/bin/env python3
"""
Comprehensive PeerDB Sync Issue Diagnosis
Specifically focuses on mes_products and mes_manufacturing_events sync problems
"""

import psycopg2
import requests
import time
import json

# Database connection parameters
PG_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'mes_db',
    'user': 'postgres',
    'password': 'postgres'
}

CH_CONFIG = {
    'host': 'localhost',
    'port': 8123,
    'user': 'default',
    'password': 'password'
}

PEERDB_CONFIG = {
    'host': 'localhost',
    'port': 9900,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'peerdb'
}

def query_clickhouse(query):
    """Execute query on ClickHouse"""
    try:
        url = f"http://{CH_CONFIG['host']}:{CH_CONFIG['port']}/"
        response = requests.post(
            url,
            data=query,
            auth=(CH_CONFIG['user'], CH_CONFIG['password']),
            timeout=10
        )
        if response.status_code == 200:
            return response.text.strip()
        else:
            return f"Error: {response.status_code} - {response.text}"
    except Exception as e:
        return f"Exception: {e}"

def query_postgresql(query, params=None):
    """Execute query on PostgreSQL"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            result = cursor.fetchall()
        else:
            conn.commit()
            result = cursor.fetchall() if cursor.description else []
        
        cursor.close()
        conn.close()
        return result
    except Exception as e:
        return f"Exception: {e}"

def query_peerdb(query):
    """Execute query on PeerDB"""
    try:
        conn = psycopg2.connect(**PEERDB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(query)
        result = cursor.fetchall()
        cursor.close()
        conn.close()
        return result
    except Exception as e:
        return f"Exception: {e}"

def check_peerdb_infrastructure():
    """Check PeerDB infrastructure status"""
    print("🔍 PeerDB Infrastructure Diagnosis")
    print("=" * 50)
    
    # Check peers
    peers_query = "SELECT name, type, created_at FROM peers ORDER BY name;"
    peers = query_peerdb(peers_query)
    
    print(f"📋 Configured Peers:")
    if isinstance(peers, str):
        print(f"   ❌ Error querying peers: {peers}")
        return False
    
    for peer in peers:
        name, peer_type, created_at = peer
        type_name = {3: 'PostgreSQL', 8: 'ClickHouse'}.get(peer_type, f'Type {peer_type}')
        print(f"   ✅ {name} ({type_name}) - Created: {created_at}")
    
    # Check mirrors/flows
    flows_query = "SELECT name FROM flows ORDER BY name;"
    flows = query_peerdb(flows_query)
    
    print(f"\n📋 Active Mirrors:")
    if isinstance(flows, str):
        print(f"   ❌ Error querying flows: {flows}")
        return False
    
    for flow in flows:
        print(f"   ✅ {flow[0]}")
    
    return len(peers) >= 2 and len(flows) >= 2

def check_table_sync_status():
    """Check specific table sync status"""
    print(f"\n🔍 Table Sync Status Analysis")
    print("=" * 40)
    
    # Check mes_products
    print(f"📊 mes_products Analysis:")
    
    # PostgreSQL count
    pg_products_count = query_postgresql("SELECT count(*) FROM mes_products")
    if isinstance(pg_products_count, str):
        print(f"   ❌ PostgreSQL error: {pg_products_count}")
    else:
        print(f"   📋 PostgreSQL records: {pg_products_count[0][0]}")
    
    # ClickHouse count
    ch_products_count = query_clickhouse("SELECT count(*) FROM default.mes_products")
    if "Exception" in ch_products_count or "Error" in ch_products_count:
        print(f"   ❌ ClickHouse error: {ch_products_count}")
    else:
        print(f"   📋 ClickHouse records: {ch_products_count}")
    
    # Check latest records
    pg_latest = query_postgresql("SELECT id, name, created_at FROM mes_products ORDER BY created_at DESC LIMIT 3")
    if not isinstance(pg_latest, str):
        print(f"   📋 Latest PostgreSQL records:")
        for record in pg_latest:
            print(f"      ID {record[0]}: {record[1]} ({record[2]})")
    
    ch_latest = query_clickhouse("SELECT id, name, created_at FROM default.mes_products ORDER BY created_at DESC LIMIT 3")
    if "Exception" not in ch_latest and "Error" not in ch_latest:
        print(f"   📋 Latest ClickHouse records:")
        for line in ch_latest.split('\n')[:3]:
            if line.strip():
                print(f"      {line}")
    
    # Check mes_manufacturing_events
    print(f"\n📊 mes_manufacturing_events Analysis:")
    
    # PostgreSQL count
    pg_events_count = query_postgresql("SELECT count(*) FROM mes_manufacturing_events")
    if isinstance(pg_events_count, str):
        print(f"   ❌ PostgreSQL error: {pg_events_count}")
    else:
        print(f"   📋 PostgreSQL records: {pg_events_count[0][0]}")
    
    # ClickHouse count
    ch_events_count = query_clickhouse("SELECT count(*) FROM default.mes_manufacturing_events")
    if "Exception" in ch_events_count or "Error" in ch_events_count:
        print(f"   ❌ ClickHouse error: {ch_events_count}")
    else:
        print(f"   📋 ClickHouse records: {ch_events_count}")
    
    # Check latest events
    pg_events_latest = query_postgresql("SELECT id, event_type, created_at FROM mes_manufacturing_events ORDER BY created_at DESC LIMIT 3")
    if not isinstance(pg_events_latest, str):
        print(f"   📋 Latest PostgreSQL events:")
        for record in pg_events_latest:
            print(f"      ID {record[0]}: {record[1]} ({record[2]})")
    
    ch_events_latest = query_clickhouse("SELECT id, event_type, created_at FROM default.mes_manufacturing_events ORDER BY created_at DESC LIMIT 3")
    if "Exception" not in ch_events_latest and "Error" not in ch_events_latest:
        print(f"   📋 Latest ClickHouse events:")
        for line in ch_events_latest.split('\n')[:3]:
            if line.strip():
                print(f"      {line}")

def test_specific_table_sync():
    """Test sync for specific problematic tables"""
    print(f"\n🧪 Live Sync Test for Problematic Tables")
    print("=" * 50)
    
    # Test mes_products sync
    print(f"📊 Testing mes_products sync...")
    
    # Insert test product
    test_product_name = f"Test_Product_Sync_{int(time.time())}"
    insert_result = query_postgresql(
        "INSERT INTO mes_products (name, description, is_active, created_at, updated_at, commodity_id) VALUES (%s, %s, %s, NOW(), NOW(), 1) RETURNING id, name",
        (test_product_name, "PeerDB sync test product", True)
    )
    
    if isinstance(insert_result, str):
        print(f"   ❌ Failed to insert test product: {insert_result}")
    else:
        product_id = insert_result[0][0]
        print(f"   ✅ Inserted test product: ID {product_id}, Name: {test_product_name}")
        
        # Wait and check ClickHouse
        print(f"   ⏳ Waiting 10 seconds for sync...")
        time.sleep(10)
        
        ch_check = query_clickhouse(f"SELECT count(*) FROM default.mes_products WHERE name = '{test_product_name}'")
        if "Exception" not in ch_check and "Error" not in ch_check:
            if ch_check == "1":
                print(f"   ✅ SUCCESS: Product synced to ClickHouse!")
            else:
                print(f"   ❌ FAILED: Product not found in ClickHouse (count: {ch_check})")
        else:
            print(f"   ❌ ClickHouse query error: {ch_check}")
    
    # Test mes_manufacturing_events sync
    print(f"\n📊 Testing mes_manufacturing_events sync...")
    
    # Insert test event
    test_event_data = f"Test_Event_Sync_{int(time.time())}"
    event_insert_result = query_postgresql(
        "INSERT INTO mes_manufacturing_events (event_type, event_data, created_at) VALUES (%s, %s, NOW()) RETURNING id, event_type",
        ("TEST_SYNC", test_event_data)
    )
    
    if isinstance(event_insert_result, str):
        print(f"   ❌ Failed to insert test event: {event_insert_result}")
    else:
        event_id = event_insert_result[0][0]
        print(f"   ✅ Inserted test event: ID {event_id}, Data: {test_event_data}")
        
        # Wait and check ClickHouse
        print(f"   ⏳ Waiting 10 seconds for sync...")
        time.sleep(10)
        
        ch_event_check = query_clickhouse(f"SELECT count(*) FROM default.mes_manufacturing_events WHERE event_data = '{test_event_data}'")
        if "Exception" not in ch_event_check and "Error" not in ch_event_check:
            if ch_event_check == "1":
                print(f"   ✅ SUCCESS: Event synced to ClickHouse!")
            else:
                print(f"   ❌ FAILED: Event not found in ClickHouse (count: {ch_event_check})")
        else:
            print(f"   ❌ ClickHouse query error: {ch_event_check}")

def check_mirror_configurations():
    """Check mirror configurations for the problematic tables"""
    print(f"\n🔍 Mirror Configuration Analysis")
    print("=" * 40)
    
    # This would require access to PeerDB's internal tables
    # Let's check what we can access
    try:
        # Try to get mirror information
        mirror_info = query_peerdb("SELECT * FROM information_schema.tables WHERE table_schema = 'public'")
        print(f"📋 Available PeerDB tables:")
        if isinstance(mirror_info, str):
            print(f"   ❌ Error: {mirror_info}")
        else:
            for table in mirror_info[:10]:  # Show first 10 tables
                print(f"   - {table[2]}")  # table_name is usually the 3rd column
    except Exception as e:
        print(f"   ❌ Cannot access PeerDB schema: {e}")

def main():
    print("🔍 PeerDB Sync Issue Comprehensive Diagnosis")
    print("=" * 60)
    print("🎯 Focus: mes_products and mes_manufacturing_events sync failures")
    
    # Step 1: Check infrastructure
    infra_ok = check_peerdb_infrastructure()
    
    # Step 2: Check table sync status
    check_table_sync_status()
    
    # Step 3: Test live sync
    test_specific_table_sync()
    
    # Step 4: Check mirror configurations
    check_mirror_configurations()
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"📊 **DIAGNOSIS SUMMARY**")
    print("=" * 60)
    
    if infra_ok:
        print(f"✅ PeerDB infrastructure appears operational")
    else:
        print(f"❌ PeerDB infrastructure issues detected")
    
    print(f"\n🎯 **RECOMMENDED ACTIONS:**")
    print(f"1. Check PeerDB dashboard: http://localhost:3000")
    print(f"2. Review mirror status and error logs")
    print(f"3. Verify table publications in PostgreSQL")
    print(f"4. Check ClickHouse table schemas match PostgreSQL")
    print(f"5. Restart PeerDB mirrors if needed")
    
    print(f"\n🔧 **TROUBLESHOOTING COMMANDS:**")
    print(f"   # Check PostgreSQL publications")
    print(f"   psql -h localhost -p 5432 -U postgres -d mes_db -c \"SELECT * FROM pg_publication_tables WHERE pubname LIKE 'peerflow%';\"")
    print(f"   ")
    print(f"   # Check ClickHouse table existence")
    print(f"   echo \"SHOW TABLES FROM default LIKE 'mes_%'\" | curl --data-binary @- \"http://localhost:8123/\" -u default:password")

if __name__ == "__main__":
    main()

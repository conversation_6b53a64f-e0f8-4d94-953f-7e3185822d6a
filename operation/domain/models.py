"""
Domain models for the new event creation system.
These models represent the core business concepts and validation results.
"""
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from enum import Enum
import uuid
from datetime import datetime


class ValidationSeverity(Enum):
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ProcessingStatus(Enum):
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"


@dataclass
class ValidationError:
    """Represents a validation error with context"""
    field: str
    message: str
    code: str
    severity: ValidationSeverity = ValidationSeverity.ERROR
    value: Any = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationResult:
    """Result of a single validator execution"""
    validator_name: str
    is_valid: bool
    errors: List[ValidationError] = field(default_factory=list)
    warnings: List[ValidationError] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time_ms: float = 0.0


@dataclass
class AggregatedValidationResult:
    """Aggregated result from the entire validation pipeline"""
    is_valid: bool
    validator_results: List[ValidationResult] = field(default_factory=list)
    total_errors: int = 0
    total_warnings: int = 0
    execution_time_ms: float = 0.0
    
    @property
    def all_errors(self) -> List[ValidationError]:
        """Returns all errors from all validators"""
        errors = []
        for result in self.validator_results:
            errors.extend(result.errors)
        return errors
    
    @property
    def all_warnings(self) -> List[ValidationError]:
        """Returns all warnings from all validators"""
        warnings = []
        for result in self.validator_results:
            warnings.extend(result.warnings)
        return warnings


@dataclass
class TransformationResult:
    """Result of event data transformation"""
    success: bool
    transformed_events: List[Dict[str, Any]] = field(default_factory=list)
    errors: List[ValidationError] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ProcessingResult:
    """Result of event processing (persistence and post-processing)"""
    status: ProcessingStatus
    created_events: List[Any] = field(default_factory=list)  # ManufacturingEvent instances
    errors: List[ValidationError] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EventCreationResult:
    """Final result of the entire event creation process"""
    success: bool
    events: List[Any] = field(default_factory=list)  # ManufacturingEvent instances
    validation_result: Optional[AggregatedValidationResult] = None
    transformation_result: Optional[TransformationResult] = None
    processing_result: Optional[ProcessingResult] = None
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    total_execution_time_ms: float = 0.0


@dataclass
class UserContext:
    """User context for event creation"""
    user_id: int
    username: str
    permissions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RequestMetadata:
    """Metadata about the HTTP request"""
    ip_address: str
    user_agent: str
    timestamp: datetime
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    headers: Dict[str, str] = field(default_factory=dict)


@dataclass
class ValidationContext:
    """Context passed to validators"""
    factory_id: str
    event_type: str
    user_context: UserContext
    request_metadata: RequestMetadata
    configuration: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TransformationContext:
    """Context passed to transformers"""
    factory_id: str
    event_type: str
    user_context: UserContext
    validation_result: AggregatedValidationResult
    configuration: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ProcessingContext:
    """Context passed to processors"""
    factory_id: str
    user_context: UserContext
    validation_result: AggregatedValidationResult
    transformation_result: TransformationResult
    configuration: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FactoryConfiguration:
    """Factory-specific configuration"""
    factory_id: str
    factory_name: str
    validation_config: Dict[str, Any] = field(default_factory=dict)
    transformation_config: Dict[str, Any] = field(default_factory=dict)
    processing_config: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

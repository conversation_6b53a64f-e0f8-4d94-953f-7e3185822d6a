"""
Core interfaces for the new event creation system.
These define the contracts that all implementations must follow.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from .models import (
    ValidationResult, ValidationContext, AggregatedValidationResult,
    TransformationResult, TransformationContext,
    ProcessingResult, ProcessingContext,
    FactoryConfiguration
)


class IEventValidator(ABC):
    """Interface for event validators"""
    
    @property
    @abstractmethod
    def validator_name(self) -> str:
        """Unique name for this validator"""
        pass
    
    @property
    @abstractmethod
    def priority(self) -> int:
        """Execution priority (lower numbers execute first)"""
        pass
    
    @abstractmethod
    def can_validate(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Determines if this validator applies to the given event type and factory"""
        pass
    
    @abstractmethod
    def validate(self, event_data: Dict[str, Any], context: ValidationContext) -> ValidationResult:
        """
        Validates event data according to specific rules
        
        Args:
            event_data: Event data to validate
            context: Validation context with factory config and user info
            
        Returns:
            ValidationResult with validation outcome
        """
        pass


class IValidationPipeline(ABC):
    """Interface for the validation pipeline"""
    
    @abstractmethod
    def add_validator(self, validator: IEventValidator) -> None:
        """Adds a validator to the pipeline"""
        pass
    
    @abstractmethod
    def remove_validator(self, validator_name: str) -> None:
        """Removes a validator from the pipeline"""
        pass
    
    @abstractmethod
    def execute(self, event_data: Dict[str, Any], context: ValidationContext) -> AggregatedValidationResult:
        """
        Executes the complete validation pipeline
        
        Args:
            event_data: Event data to validate
            context: Validation context
            
        Returns:
            AggregatedValidationResult with all validation outcomes
        """
        pass


class IEventTransformer(ABC):
    """Interface for event transformers"""
    
    @property
    @abstractmethod
    def transformer_name(self) -> str:
        """Unique name for this transformer"""
        pass
    
    @abstractmethod
    def can_transform(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Determines if this transformer applies to the given event type and factory"""
        pass
    
    @abstractmethod
    def transform(self, event_data: Dict[str, Any], context: TransformationContext) -> TransformationResult:
        """
        Transforms raw event data into canonical format
        
        Args:
            event_data: Raw event data
            context: Transformation context
            
        Returns:
            TransformationResult with transformed events or errors
        """
        pass


class IEventTransformationService(ABC):
    """Interface for the event transformation service"""
    
    @abstractmethod
    def transform_event(self, event_data: Dict[str, Any], context: TransformationContext) -> TransformationResult:
        """
        Transforms event data using appropriate transformer
        
        Args:
            event_data: Raw event data
            context: Transformation context
            
        Returns:
            TransformationResult with transformed events
        """
        pass


class IEventProcessor(ABC):
    """Interface for event processors"""
    
    @property
    @abstractmethod
    def processor_name(self) -> str:
        """Unique name for this processor"""
        pass
    
    @property
    @abstractmethod
    def processing_order(self) -> int:
        """Processing order (lower numbers process first)"""
        pass
    
    @abstractmethod
    def can_process(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Determines if this processor applies to the given event type and factory"""
        pass
    
    @abstractmethod
    def process(self, events: List[Dict[str, Any]], context: ProcessingContext) -> ProcessingResult:
        """
        Processes validated and transformed events
        
        Args:
            events: List of events to process
            context: Processing context
            
        Returns:
            ProcessingResult with processing outcome
        """
        pass


class IEventProcessingService(ABC):
    """Interface for the event processing service"""
    
    @abstractmethod
    def process_events(self, events: List[Dict[str, Any]], context: ProcessingContext) -> ProcessingResult:
        """
        Processes events using appropriate processors
        
        Args:
            events: Events to process
            context: Processing context
            
        Returns:
            ProcessingResult with processing outcome
        """
        pass


class IConfigurationService(ABC):
    """Interface for configuration management"""
    
    @abstractmethod
    def get_factory_configuration(self, factory_id: str) -> FactoryConfiguration:
        """Retrieves factory-specific configuration"""
        pass
    
    @abstractmethod
    def get_validation_config(self, factory_id: str) -> Dict[str, Any]:
        """Retrieves validation configuration for factory"""
        pass
    
    @abstractmethod
    def get_transformation_config(self, factory_id: str) -> Dict[str, Any]:
        """Retrieves transformation configuration for factory"""
        pass
    
    @abstractmethod
    def get_processing_config(self, factory_id: str) -> Dict[str, Any]:
        """Retrieves processing configuration for factory"""
        pass


class IEventRepository(ABC):
    """Interface for event persistence"""
    
    @abstractmethod
    def save_events(self, events: List[Dict[str, Any]]) -> List[Any]:
        """
        Saves events to the database
        
        Args:
            events: List of event data to save
            
        Returns:
            List of created ManufacturingEvent instances
        """
        pass
    
    @abstractmethod
    def update_routing_execution(self, serial_number: str, event_ids: List[int]) -> bool:
        """
        Updates routing execution with event IDs
        
        Args:
            serial_number: Serial number to update
            event_ids: List of event IDs to associate
            
        Returns:
            True if successful, False otherwise
        """
        pass

"""
New event creation API (v2) using the redesigned architecture.
This runs parallel to the existing event_views.py without affecting it.
"""
import logging
from datetime import datetime
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.request import Request
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from authentication.decorators import requires_permission
from ..application.commands import CreateManufacturingEventCommand, ValidateEventCommand
from ..application.handlers import CreateManufacturingEventCommandHandler, ValidateEventCommandHandler
from ..domain.models import UserContext, RequestMetadata, ValidationSeverity
from ..infrastructure.container import get_container
from ..serializers.event_serializers import ManufacturingEventSummarySerializer

logger = logging.getLogger(__name__)


class ManufacturingEventV2ViewSet(viewsets.ViewSet):
    """
    New manufacturing event API using the redesigned architecture.
    Implements Command Pattern with proper separation of concerns.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.container = get_container()
    
    @swagger_auto_schema(
        operation_description="Create manufacturing events using the new architecture",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'form': openapi.Schema(type=openapi.TYPE_INTEGER, description='Form ID'),
                'serial_number': openapi.Schema(type=openapi.TYPE_STRING, description='Serial number'),
                'event_data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Event data'),
                'timestamp': openapi.Schema(type=openapi.TYPE_STRING, description='Event timestamp'),
                'inspection_status': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Inspection status'),
                'next_action': openapi.Schema(type=openapi.TYPE_STRING, description='Next action'),
                'validation_mode': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    enum=['strict', 'lenient'],
                    description='Validation mode (optional, defaults to strict)'
                ),
                'dry_run': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Perform validation and transformation without persisting (optional)'
                )
            },
            required=['form', 'serial_number', 'event_data', 'timestamp']
        ),
        responses={
            201: openapi.Response(
                description="Events created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'events': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'validation_summary': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'correlation_id': openapi.Schema(type=openapi.TYPE_STRING),
                        'execution_time_ms': openapi.Schema(type=openapi.TYPE_NUMBER)
                    }
                )
            ),
            400: openapi.Response(description="Validation failed"),
            500: openapi.Response(description="Internal server error")
        }
    )
    @requires_permission(('operation', 'add'))
    def create(self, request: Request) -> Response:
        """
        Creates manufacturing events using the new architecture.
        """
        try:
            logger.info(f"Received event creation request from user {request.user.id}")
            
            # Create user context
            user_context = UserContext(
                user_id=request.user.id,
                username=request.user.username,
                permissions=[]  # Could be populated from user permissions
            )
            
            # Create request metadata
            request_metadata = RequestMetadata(
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                timestamp=datetime.now(),
                headers=dict(request.headers)
            )
            
            # Extract processing options from request
            validation_mode = request.data.get('validation_mode', 'strict')
            dry_run = request.data.get('dry_run', False)
            
            # Create command
            command = CreateManufacturingEventCommand(
                event_data=request.data,
                factory_id='default',  # Could be extracted from request or user context
                user_context=user_context,
                request_metadata=request_metadata,
                validation_mode=validation_mode,
                dry_run=dry_run
            )
            
            # Get command handler from container
            handler = self._get_command_handler()
            
            # Execute command
            result = handler.handle(command)
            
            # Format response
            response_data = self._format_creation_response(result)
            
            if result.success:
                logger.info(f"Event creation successful: {result.correlation_id}")
                return Response(response_data, status=status.HTTP_201_CREATED)
            else:
                logger.warning(f"Event creation failed: {result.correlation_id}")
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
                
        except ValueError as e:
            logger.error(f"Invalid command data: {str(e)}")
            return Response({
                'success': False,
                'error': 'Invalid request data',
                'details': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            logger.error(f"Unexpected error in event creation: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': 'An unexpected error occurred'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @swagger_auto_schema(
        operation_description="Validate event data without creating events",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'form': openapi.Schema(type=openapi.TYPE_INTEGER, description='Form ID'),
                'serial_number': openapi.Schema(type=openapi.TYPE_STRING, description='Serial number'),
                'event_data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Event data'),
                'timestamp': openapi.Schema(type=openapi.TYPE_STRING, description='Event timestamp'),
                'validation_mode': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['strict', 'lenient'],
                    description='Validation mode (optional)'
                )
            },
            required=['form', 'serial_number', 'event_data', 'timestamp']
        ),
        responses={
            200: openapi.Response(
                description="Validation completed",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'is_valid': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'errors': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'warnings': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'execution_time_ms': openapi.Schema(type=openapi.TYPE_NUMBER)
                    }
                )
            )
        }
    )
    @action(detail=False, methods=['post'])
    @requires_permission(('operation', 'add'))
    def validate(self, request: Request) -> Response:
        """
        Validates event data without creating events.
        """
        try:
            logger.info(f"Received event validation request from user {request.user.id}")
            
            # Create user context
            user_context = UserContext(
                user_id=request.user.id,
                username=request.user.username
            )
            
            # Create validation command
            command = ValidateEventCommand(
                event_data=request.data,
                factory_id='default',
                user_context=user_context,
                validation_mode=request.data.get('validation_mode', 'strict')
            )
            
            # Get validation handler from container
            from ..domain.interfaces import IValidationPipeline, IConfigurationService
            validation_pipeline = self.container.resolve(IValidationPipeline)
            configuration_service = self.container.resolve(IConfigurationService)
            handler = ValidateEventCommandHandler(validation_pipeline, configuration_service)
            
            # Execute validation
            result = handler.handle(command)
            
            # Format response
            response_data = {
                'is_valid': result.is_valid,
                'errors': [self._format_validation_error(error) for error in result.all_errors],
                'warnings': [self._format_validation_error(warning) for warning in result.all_warnings],
                'execution_time_ms': result.execution_time_ms,
                'correlation_id': command.correlation_id
            }
            
            logger.info(f"Event validation completed: {command.correlation_id}, valid: {result.is_valid}")
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error in event validation: {str(e)}", exc_info=True)
            return Response({
                'is_valid': False,
                'error': 'Validation failed',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _get_command_handler(self) -> CreateManufacturingEventCommandHandler:
        """Gets the command handler from the container"""
        from ..domain.interfaces import IValidationPipeline, IEventTransformationService, IEventProcessingService, IConfigurationService
        
        return CreateManufacturingEventCommandHandler(
            validation_pipeline=self.container.resolve(IValidationPipeline),
            transformation_service=self.container.resolve(IEventTransformationService),
            processing_service=self.container.resolve(IEventProcessingService),
            configuration_service=self.container.resolve(IConfigurationService)
        )
    
    def _format_creation_response(self, result) -> dict:
        """Formats the event creation response"""
        response = {
            'success': result.success,
            'correlation_id': result.correlation_id,
            'execution_time_ms': result.total_execution_time_ms
        }
        
        if result.success and result.events:
            # Serialize created events
            serializer = ManufacturingEventSummarySerializer(result.events, many=True)
            response['events'] = serializer.data
        
        # Add validation summary
        if result.validation_result:
            response['validation_summary'] = {
                'is_valid': result.validation_result.is_valid,
                'total_errors': result.validation_result.total_errors,
                'total_warnings': result.validation_result.total_warnings
            }
            
            if not result.validation_result.is_valid:
                response['validation_errors'] = [
                    self._format_validation_error(error) 
                    for error in result.validation_result.all_errors
                ]
        
        # Add transformation info
        if result.transformation_result:
            response['transformation_summary'] = {
                'success': result.transformation_result.success,
                'events_created': len(result.transformation_result.transformed_events)
            }
        
        return response
    
    def _format_validation_error(self, error) -> dict:
        """Formats a validation error for the response"""
        return {
            'field': error.field,
            'message': error.message,
            'code': error.code,
            'severity': error.severity.value,
            'value': error.value
        }
    
    def _get_client_ip(self, request: Request) -> str:
        """Extracts client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'

"""
Simplified event creation API (v2) with functional parity to v1 API.
This implementation maintains atomic transaction behavior and reduces complexity.
"""
import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.request import Request
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from authentication.decorators import requires_permission
from ..services.event_creation_service_v2 import EventCreationServiceV2
from ..serializers.event_serializers import ManufacturingEventSummarySerializer

logger = logging.getLogger(__name__)


class ManufacturingEventV2ViewSet(viewsets.ViewSet):
    """
    Simplified manufacturing event API with functional parity to v1 API.
    Maintains atomic transaction behavior while providing cleaner implementation.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.event_service = EventCreationServiceV2()
    
    @swagger_auto_schema(
        operation_description="Create manufacturing events with simplified architecture",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'form': openapi.Schema(type=openapi.TYPE_INTEGER, description='Form ID'),
                'serial_number': openapi.Schema(type=openapi.TYPE_STRING, description='Serial number'),
                'event_data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Event data'),
                'timestamp': openapi.Schema(type=openapi.TYPE_STRING, description='Event timestamp'),
                'inspection_status': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Inspection status'),
                'next_action': openapi.Schema(type=openapi.TYPE_STRING, description='Next action')
            },
            required=['form', 'serial_number', 'event_data', 'timestamp']
        ),
        responses={
            201: openapi.Response(
                description="Events created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'events': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'errors': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'warnings': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'execution_time_ms': openapi.Schema(type=openapi.TYPE_NUMBER)
                    }
                )
            ),
            400: openapi.Response(description="Validation failed"),
            500: openapi.Response(description="Internal server error")
        }
    )
    @requires_permission(('operation', 'add'))
    def create(self, request: Request) -> Response:
        """
        Creates manufacturing events using simplified service with atomic transactions.
        """
        try:
            logger.info(f"Received event creation request from user {request.user.id}")

            # Use the simplified service to create events
            result = self.event_service.create_events(
                event_data=request.data,
                user=request.user
            )

            # Format response
            response_data = {
                'success': result.success,
                'execution_time_ms': result.execution_time_ms
            }

            if result.success:
                # Serialize created events
                if result.events:
                    serializer = ManufacturingEventSummarySerializer(result.events, many=True)
                    response_data['events'] = serializer.data

                # Add warnings if any
                if result.warnings:
                    response_data['warnings'] = result.warnings

                logger.info(f"Event creation successful in {result.execution_time_ms:.2f}ms")
                return Response(response_data, status=status.HTTP_201_CREATED)
            else:
                # Add errors
                response_data['errors'] = result.errors

                logger.warning(f"Event creation failed: {result.errors}")
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Unexpected error in event creation: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'errors': [{'field': 'system', 'message': 'An unexpected error occurred'}],
                'execution_time_ms': 0
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @swagger_auto_schema(
        operation_description="Validate event data without creating events",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'form': openapi.Schema(type=openapi.TYPE_INTEGER, description='Form ID'),
                'serial_number': openapi.Schema(type=openapi.TYPE_STRING, description='Serial number'),
                'event_data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Event data'),
                'timestamp': openapi.Schema(type=openapi.TYPE_STRING, description='Event timestamp')
            },
            required=['form', 'serial_number', 'event_data', 'timestamp']
        ),
        responses={
            200: openapi.Response(
                description="Validation completed",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'is_valid': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'errors': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'warnings': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'execution_time_ms': openapi.Schema(type=openapi.TYPE_NUMBER)
                    }
                )
            )
        }
    )
    @action(detail=False, methods=['post'])
    @requires_permission(('operation', 'add'))
    def validate(self, request: Request) -> Response:
        """
        Validates event data without creating events using simplified service.
        """
        try:
            logger.info(f"Received event validation request from user {request.user.id}")

            # Use the simplified service to validate events
            result = self.event_service.validate_events(event_data=request.data)

            # Format response
            response_data = {
                'is_valid': result.success,
                'errors': result.errors,
                'warnings': result.warnings,
                'execution_time_ms': result.execution_time_ms
            }

            logger.info(f"Event validation completed: valid={result.success}")
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in event validation: {str(e)}", exc_info=True)
            return Response({
                'is_valid': False,
                'errors': [{'field': 'system', 'message': 'Validation failed'}],
                'execution_time_ms': 0
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


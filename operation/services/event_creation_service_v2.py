"""
Simplified event creation service for v2 API.
This service maintains the same transactional behavior as the v1 API
while providing a cleaner, more maintainable implementation.
"""
import logging
from typing import Dict, List, Any, Tuple, Optional
from django.db import transaction
from authentication.models import User
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework.exceptions import ValidationError as DRFValidationError

logger = logging.getLogger(__name__)


class EventCreationResult:
    """Simple result object for event creation operations"""

    def __init__(self, success: bool, events: List = None, errors: List = None, 
                 warnings: List = None, execution_time_ms: float = 0.0):
        self.success = success
        self.events = events or []
        self.errors = errors or []
        self.warnings = warnings or []
        self.execution_time_ms = execution_time_ms


class EventCreationServiceV2:
    """
    Simplified event creation service that maintains v1 API functional parity.

    This service handles the complete event creation flow in a single atomic transaction:
    1. FIFO validation
    2. Routing validation
    3. Event transformation
    4. Event persistence
    5. Routing execution updates
    6. FIFO violation logging

    All operations are atomic - if any step fails, no database changes are made.
    """

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def create_events(self, event_data: Dict[str, Any], user: User) -> EventCreationResult:
        """
        Creates manufacturing events with the same behavior as the v1 API.

        Args:
            event_data: Raw event data from the request
            user: user creating the event

        Returns:
            EventCreationResult with success status and created events or errors
        """
        import time
        start_time = time.time()

        try:
            self.logger.info(f"Starting event creation for user {user}")

            # Use atomic transaction to ensure all-or-nothing behavior
            with transaction.atomic():
                # Step 1: FIFO Validation
                fifo_valid, fifo_message = self._validate_fifo(event_data)
                if not fifo_valid:
                    self.logger.warning(f"FIFO validation failed: {fifo_message}")
                    return EventCreationResult(
                        success=False,
                        errors=[{"field": "fifo", "message": fifo_message}],
                        execution_time_ms=(time.time() - start_time) * 1000
                    )

                # # Step 1.5: Routing Validation
                # NOT NECESSARY here since the same is being done in the _transform_event_data step 2

                # Step 2: Transform event data
                transformed_events = self._transform_event_data(event_data)
                if not transformed_events:
                    return EventCreationResult(
                        success=False,
                        errors=[{"field": "transformation", "message": "Event transformation failed"}],
                        execution_time_ms=(time.time() - start_time) * 1000
                    )

                # Step 3: Create events
                created_events = self._create_events(transformed_events, user)

                # Step 4: Update routing execution
                self._update_routing_execution(created_events)

                # Step 5: Update FIFO violation logs
                # NOT NECESSARY here since the same is being done in the validate_event_with_fifo

                execution_time = (time.time() - start_time) * 1000
                self.logger.info(f"Event creation completed successfully in {execution_time:.2f}ms")

                warnings = []
                if fifo_message:  # FIFO warning (allowed to proceed)
                    warnings.append({"field": "fifo", "message": fifo_message})

                return EventCreationResult(
                    success=True,
                    events=created_events,
                    warnings=warnings,
                    execution_time_ms=execution_time
                )

        except Exception as e:
            self.logger.error(f"Event creation failed: {str(e)}", exc_info=True)
            return EventCreationResult(
                success=False,
                errors=[{"field": "system", "message": f"Event creation failed: {str(e)}"}],
                execution_time_ms=(time.time() - start_time) * 1000
            )

    def validate_events(self, event_data: Dict[str, Any]) -> EventCreationResult:
        """
        Validates event data without creating events.

        Args:
            event_data: Raw event data to validate

        Returns:
            EventCreationResult with validation results
        """
        import time
        start_time = time.time()

        try:
            self.logger.info("Starting event validation")

            errors = []
            warnings = []

            # FIFO validation
            fifo_valid, fifo_message = self._validate_fifo(event_data)
            if not fifo_valid:
                errors.append({"field": "fifo", "message": fifo_message})
            elif fifo_message:
                warnings.append({"field": "fifo", "message": fifo_message})

            # Basic schema validation
            schema_errors = self._validate_schema(event_data)
            errors.extend(schema_errors)

            # Routing validation (without database updates)
            routing_errors = self._validate_routing_only(event_data)
            errors.extend(routing_errors)

            execution_time = (time.time() - start_time) * 1000
            is_valid = len(errors) == 0

            self.logger.info(f"Event validation completed: valid={is_valid}, "
                           f"errors={len(errors)}, warnings={len(warnings)}")

            return EventCreationResult(
                success=is_valid,
                errors=errors,
                warnings=warnings,
                execution_time_ms=execution_time
            )

        except Exception as e:
            self.logger.error(f"Event validation failed: {str(e)}", exc_info=True)
            return EventCreationResult(
                success=False,
                errors=[{"field": "system", "message": f"Validation failed: {str(e)}"}],
                execution_time_ms=(time.time() - start_time) * 1000
            )

    def _validate_fifo(self, event_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """FIFO validation using existing v1 logic"""
        try:
            from operation.services.fifo_validation_service import validate_event_with_fifo
            return validate_event_with_fifo(event_data)
        except Exception as e:
            self.logger.error(f"FIFO validation error: {str(e)}")
            return False, f"FIFO validation failed: {str(e)}"

    def _validate_routing(self, event_data: Dict[str, Any]) -> Tuple[bool, List[Dict[str, str]]]:
        """Routing validation using existing v1 logic"""
        try:
            from workflow_config.services.routing_validation_service import RoutingValidationService

            is_valid, validation_errors, next_executable = RoutingValidationService.validate_event(event_data)

            # Convert validation errors to our format
            errors = []
            for error in validation_errors or []:
                errors.append({
                    "field": error.get('field', 'routing'),
                    "message": error.get('error', 'Routing validation failed')
                })

            return is_valid, errors

        except Exception as e:
            self.logger.error(f"Routing validation error: {str(e)}")
            return False, [{"field": "routing", "message": f"Routing validation failed: {str(e)}"}]

    def _validate_schema(self, event_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Basic schema validation"""
        errors = []

        required_fields = ['form', 'serial_number', 'event_data', 'timestamp']
        for field in required_fields:
            if field not in event_data or event_data[field] is None:
                errors.append({
                    "field": field,
                    "message": f"Required field '{field}' is missing or null"
                })

        return errors

    def _validate_routing_only(self, event_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Routing validation without database updates"""
        try:
            from workflow_config.services.routing_validation_service import RoutingValidationService

            # This will validate but also update the database, which we don't want for validation-only
            # For now, we'll skip routing validation in validation-only mode
            # In a production system, we'd need a separate validation-only method
            return []

        except Exception as e:
            self.logger.error(f"Routing validation error: {str(e)}")
            return [{"field": "routing", "message": f"Routing validation failed: {str(e)}"}]

    def _transform_event_data(self, event_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Transform event data using existing v1 logic"""
        try:
            from operation.services.event_service import ManufacturingEventService
            return ManufacturingEventService.transform_event_data(event_data)
        except Exception as e:
            self.logger.error(f"Event transformation error: {str(e)}")
            return []

    def _create_events(self, transformed_events: List[Dict[str, Any]], user: User) -> List:
        """Create events using Django serializers"""
        try:
            from operation.serializers.event_serializers import ManufacturingEventSerializer

            # Add user_id to each event
            for event_data in transformed_events:
                event_data['created_by_id'] = user.id

            serializer = ManufacturingEventSerializer(data=transformed_events, many=True)
            serializer.is_valid(raise_exception=True)
            return serializer.save(created_by=user)

        except Exception as e:
            self.logger.error(f"Event creation error: {str(e)}")
            raise

    def _update_routing_execution(self, created_events: List) -> None:
        """Update routing execution using existing v1 logic"""
        try:
            from workflow_config.services.routing_validation_service import RoutingValidationService

            # Group events by serial number
            events_by_serial = {}
            for event in created_events:
                if event.serial_number not in events_by_serial:
                    events_by_serial[event.serial_number] = []
                events_by_serial[event.serial_number].append(event.id)

            # Update routing execution for each serial number
            for serial_number, event_ids in events_by_serial.items():
                RoutingValidationService.update_routing_execution_with_event_ids(
                    serial_number, event_ids
                )

        except Exception as e:
            self.logger.error(f"Routing execution update error: {str(e)}")
            raise

    def _update_fifo_violation_logs(self, created_events: List) -> None:
        """Update FIFO violation logs using existing v1 logic"""
        try:
            from operation.models import FIFOViolationLog

            # Update FIFO violation logs
            for event in created_events:
                FIFOViolationLog.objects.filter(
                    serial_number=event.serial_number,
                    event__isnull=True
                ).update(event=event)

        except Exception as e:
            self.logger.error(f"FIFO violation log update error: {str(e)}")
            # Don't raise - this is not critical
            pass

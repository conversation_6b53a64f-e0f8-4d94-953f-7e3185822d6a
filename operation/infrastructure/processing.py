"""
Event processing services for the new event creation system.
"""
import logging
from typing import Dict, List, Any
from ..domain.interfaces import IEventProcessor, IEventProcessingService, IEventRepository
from ..domain.models import (
    ProcessingResult, ProcessingContext, ProcessingStatus,
    ValidationError, ValidationSeverity, FactoryConfiguration
)

logger = logging.getLogger(__name__)


class EventProcessingService(IEventProcessingService):
    """
    Service that manages event processing using registered processors.
    """
    
    def __init__(self, event_repository: IEventRepository):
        self.event_repository = event_repository
        self._processors: List[IEventProcessor] = []
    
    def add_processor(self, processor: IEventProcessor) -> None:
        """Adds a processor to the service"""
        self._processors.append(processor)
        # Sort by processing order
        self._processors.sort(key=lambda p: p.processing_order)
        logger.info(f"Added processor {processor.processor_name} with order {processor.processing_order}")
    
    def process_events(self, events: List[Dict[str, Any]], context: ProcessingContext) -> ProcessingResult:
        """
        Processes events using appropriate processors
        """
        logger.info(f"Starting event processing for {len(events)} events")
        
        try:
            # Create factory config from context
            factory_config = FactoryConfiguration(
                factory_id=context.factory_id,
                factory_name=context.factory_id,
                processing_config=context.configuration
            )
            
            # Find applicable processors
            applicable_processors = []
            for processor in self._processors:
                event_type = events[0].get('event_type', 'unknown') if events else 'unknown'
                if processor.can_process(event_type, factory_config):
                    applicable_processors.append(processor)
            
            if not applicable_processors:
                logger.info("No specific processors found, using default processing")
                return self._default_processing(events, context)
            
            # Execute processors in order
            current_events = events
            all_created_events = []
            all_errors = []

            for processor in applicable_processors:
                logger.debug(f"Executing processor {processor.processor_name}")

                # Update context with previous results for processors that need them
                updated_context = ProcessingContext(
                    factory_id=context.factory_id,
                    user_context=context.user_context,
                    validation_result=context.validation_result,
                    transformation_result=context.transformation_result,
                    configuration=context.configuration
                )
                # Add created events to context metadata for processors that need them
                updated_context.metadata = getattr(context, 'metadata', {}).copy()
                updated_context.metadata['created_events'] = all_created_events

                result = processor.process(current_events, updated_context)

                if result.status == ProcessingStatus.FAILED:
                    logger.error(f"Processor {processor.processor_name} failed")
                    all_errors.extend(result.errors)
                    return ProcessingResult(
                        status=ProcessingStatus.FAILED,
                        created_events=all_created_events,
                        errors=all_errors
                    )

                all_created_events.extend(result.created_events)
                all_errors.extend(result.errors)

                # Update current events for next processor if needed
                if result.metadata.get('update_events'):
                    current_events = result.metadata['updated_events']
            
            final_status = ProcessingStatus.SUCCESS if not all_errors else ProcessingStatus.PARTIAL
            
            logger.info(f"Event processing completed: status={final_status.value}, "
                       f"created_events={len(all_created_events)}, errors={len(all_errors)}")
            
            return ProcessingResult(
                status=final_status,
                created_events=all_created_events,
                errors=all_errors
            )
            
        except Exception as e:
            logger.error(f"Error during event processing: {str(e)}", exc_info=True)
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                errors=[ValidationError(
                    field="processing",
                    message=f"Event processing failed: {str(e)}",
                    code="PROCESSING_ERROR",
                    severity=ValidationSeverity.ERROR
                )]
            )
    
    def _default_processing(self, events: List[Dict[str, Any]], context: ProcessingContext) -> ProcessingResult:
        """Default processing when no specific processors are found"""
        try:
            # Use the standard persistence processor
            persistence_processor = PersistenceProcessor(self.event_repository)
            return persistence_processor.process(events, context)
            
        except Exception as e:
            logger.error(f"Error in default processing: {str(e)}", exc_info=True)
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                errors=[ValidationError(
                    field="processing",
                    message=f"Default processing failed: {str(e)}",
                    code="DEFAULT_PROCESSING_ERROR"
                )]
            )


class PersistenceProcessor(IEventProcessor):
    """
    Processor that handles event persistence to the database.
    """
    
    def __init__(self, event_repository: IEventRepository):
        self.event_repository = event_repository
    
    @property
    def processor_name(self) -> str:
        return "persistence_processor"
    
    @property
    def processing_order(self) -> int:
        return 10  # Run early in the processing pipeline
    
    def can_process(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Persistence applies to all events"""
        return True
    
    def process(self, events: List[Dict[str, Any]], context: ProcessingContext) -> ProcessingResult:
        """
        Persists events to the database
        """
        try:
            logger.info(f"Persisting {len(events)} events to database")
            
            # Save events using repository
            created_events = self.event_repository.save_events(events)
            
            logger.info(f"Successfully persisted {len(created_events)} events")
            
            return ProcessingResult(
                status=ProcessingStatus.SUCCESS,
                created_events=created_events,
                metadata={'persisted_count': len(created_events)}
            )
            
        except Exception as e:
            logger.error(f"Error persisting events: {str(e)}", exc_info=True)
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                errors=[ValidationError(
                    field="persistence",
                    message=f"Failed to persist events: {str(e)}",
                    code="PERSISTENCE_ERROR"
                )]
            )


class RoutingUpdateProcessor(IEventProcessor):
    """
    Processor that updates routing execution after event creation.
    """
    
    def __init__(self, event_repository: IEventRepository):
        self.event_repository = event_repository
    
    @property
    def processor_name(self) -> str:
        return "routing_update_processor"
    
    @property
    def processing_order(self) -> int:
        return 20  # Run after persistence
    
    def can_process(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Routing updates apply to all events"""
        return True
    
    def process(self, events: List[Dict[str, Any]], context: ProcessingContext) -> ProcessingResult:
        """
        Updates routing execution with created events
        """
        try:
            logger.info(f"Updating routing execution for {len(events)} events")

            # Get created events from context metadata (set by previous processors)
            created_events = context.metadata.get('created_events', [])

            if not created_events:
                logger.warning("No created events found in context for routing update")
                return ProcessingResult(
                    status=ProcessingStatus.SUCCESS,
                    metadata={'routing_updates': 0}
                )

            # Group events by serial number
            events_by_serial = {}
            for event in created_events:
                serial_number = getattr(event, 'serial_number', None)
                if serial_number:
                    if serial_number not in events_by_serial:
                        events_by_serial[serial_number] = []
                    events_by_serial[serial_number].append(event.id)
            
            # Update routing execution for each serial number
            update_count = 0
            errors = []
            
            for serial_number, event_ids in events_by_serial.items():
                try:
                    success = self.event_repository.update_routing_execution(serial_number, event_ids)
                    if success:
                        update_count += 1
                    else:
                        errors.append(ValidationError(
                            field="routing_update",
                            message=f"Failed to update routing for serial number {serial_number}",
                            code="ROUTING_UPDATE_FAILED",
                            value=serial_number
                        ))
                except Exception as e:
                    logger.error(f"Error updating routing for {serial_number}: {str(e)}")
                    errors.append(ValidationError(
                        field="routing_update",
                        message=f"Error updating routing for {serial_number}: {str(e)}",
                        code="ROUTING_UPDATE_ERROR",
                        value=serial_number
                    ))
            
            status = ProcessingStatus.SUCCESS if not errors else ProcessingStatus.PARTIAL
            
            logger.info(f"Routing update completed: updated={update_count}, errors={len(errors)}")
            
            return ProcessingResult(
                status=status,
                errors=errors,
                metadata={'routing_updates': update_count}
            )
            
        except Exception as e:
            logger.error(f"Error in routing update processor: {str(e)}", exc_info=True)
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                errors=[ValidationError(
                    field="routing_update",
                    message=f"Routing update processor failed: {str(e)}",
                    code="ROUTING_UPDATE_PROCESSOR_ERROR"
                )]
            )

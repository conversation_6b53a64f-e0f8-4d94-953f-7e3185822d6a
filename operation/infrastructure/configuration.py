"""
Configuration services for the new event creation system.
"""
import logging
from typing import Dict, Any
from ..domain.interfaces import IConfigurationService
from ..domain.models import FactoryConfiguration

logger = logging.getLogger(__name__)


class ConfigurationService(IConfigurationService):
    """
    Simple configuration service that provides factory-specific configurations.
    In a production system, this would load from files, database, or external config service.
    """
    
    def __init__(self):
        # Default configuration - in practice this would be loaded from external source
        self._default_config = {
            'validation': {
                'early_exit_on_error': False,
                'strict_mode': True,
                'enable_warnings': True
            },
            'transformation': {
                'default_transformer': 'default_transformer',
                'enable_corrected_events': True
            },
            'processing': {
                'enable_routing_updates': True,
                'batch_size': 100
            }
        }
        
        # Factory-specific configurations
        self._factory_configs = {
            'default': self._default_config,
            'factory_001': {
                **self._default_config,
                'validation': {
                    **self._default_config['validation'],
                    'early_exit_on_error': True
                }
            }
        }
    
    def get_factory_configuration(self, factory_id: str) -> FactoryConfiguration:
        """
        Retrieves factory-specific configuration
        """
        logger.debug(f"Getting configuration for factory: {factory_id}")
        
        # Get factory config or fall back to default
        config = self._factory_configs.get(factory_id, self._default_config)
        
        return FactoryConfiguration(
            factory_id=factory_id,
            factory_name=f"Factory {factory_id}",
            validation_config=config.get('validation', {}),
            transformation_config=config.get('transformation', {}),
            processing_config=config.get('processing', {}),
            metadata={'config_source': 'internal'}
        )
    
    def get_validation_config(self, factory_id: str) -> Dict[str, Any]:
        """
        Retrieves validation configuration for factory
        """
        factory_config = self.get_factory_configuration(factory_id)
        return factory_config.validation_config
    
    def get_transformation_config(self, factory_id: str) -> Dict[str, Any]:
        """
        Retrieves transformation configuration for factory
        """
        factory_config = self.get_factory_configuration(factory_id)
        return factory_config.transformation_config
    
    def get_processing_config(self, factory_id: str) -> Dict[str, Any]:
        """
        Retrieves processing configuration for factory
        """
        factory_config = self.get_factory_configuration(factory_id)
        return factory_config.processing_config
    
    def add_factory_config(self, factory_id: str, config: Dict[str, Any]) -> None:
        """
        Adds or updates factory-specific configuration
        """
        logger.info(f"Adding/updating configuration for factory: {factory_id}")
        self._factory_configs[factory_id] = {
            **self._default_config,
            **config
        }
    
    def reload_configuration(self) -> None:
        """
        Reloads configuration from source.
        In a production system, this would reload from files/database.
        """
        logger.info("Configuration reload requested - using in-memory config")
        # In practice, this would reload from external source
        pass


class FileConfigurationService(IConfigurationService):
    """
    Configuration service that loads from YAML files.
    This is an example of how to extend the configuration system.
    """
    
    def __init__(self, config_directory: str = "config"):
        self.config_directory = config_directory
        self._cached_configs = {}
        self._load_configurations()
    
    def _load_configurations(self) -> None:
        """
        Loads configurations from YAML files.
        This is a placeholder - in practice would use yaml.safe_load()
        """
        # Placeholder implementation
        self._cached_configs = {
            'default': {
                'validation': {
                    'early_exit_on_error': False,
                    'strict_mode': True
                },
                'transformation': {
                    'default_transformer': 'default_transformer'
                },
                'processing': {
                    'enable_routing_updates': True
                }
            }
        }
        logger.info(f"Loaded configurations for {len(self._cached_configs)} factories")
    
    def get_factory_configuration(self, factory_id: str) -> FactoryConfiguration:
        """
        Retrieves factory-specific configuration from files
        """
        config = self._cached_configs.get(factory_id, self._cached_configs.get('default', {}))
        
        return FactoryConfiguration(
            factory_id=factory_id,
            factory_name=f"Factory {factory_id}",
            validation_config=config.get('validation', {}),
            transformation_config=config.get('transformation', {}),
            processing_config=config.get('processing', {}),
            metadata={'config_source': 'file', 'config_directory': self.config_directory}
        )
    
    def get_validation_config(self, factory_id: str) -> Dict[str, Any]:
        """Retrieves validation configuration for factory"""
        factory_config = self.get_factory_configuration(factory_id)
        return factory_config.validation_config
    
    def get_transformation_config(self, factory_id: str) -> Dict[str, Any]:
        """Retrieves transformation configuration for factory"""
        factory_config = self.get_factory_configuration(factory_id)
        return factory_config.transformation_config
    
    def get_processing_config(self, factory_id: str) -> Dict[str, Any]:
        """Retrieves processing configuration for factory"""
        factory_config = self.get_factory_configuration(factory_id)
        return factory_config.processing_config
    
    def reload_configuration(self) -> None:
        """Reloads configuration from files"""
        logger.info("Reloading configuration from files")
        self._cached_configs.clear()
        self._load_configurations()

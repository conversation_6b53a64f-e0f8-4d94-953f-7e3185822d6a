"""
Validation pipeline and core validators for the new event creation system.
"""
import time
import logging
from typing import Dict, List, Any
from ..domain.interfaces import IEventValidator, IValidationPipeline
from ..domain.models import (
    ValidationResult, ValidationContext, AggregatedValidationResult,
    ValidationError, ValidationSeverity, FactoryConfiguration
)

logger = logging.getLogger(__name__)


class ValidationPipeline(IValidationPipeline):
    """
    Concrete implementation of the validation pipeline.
    Executes validators in priority order.
    """
    
    def __init__(self):
        self._validators: List[IEventValidator] = []
    
    def add_validator(self, validator: IEventValidator) -> None:
        """Adds a validator to the pipeline"""
        self._validators.append(validator)
        # Sort by priority to maintain execution order
        self._validators.sort(key=lambda v: v.priority)
        logger.info(f"Added validator {validator.validator_name} with priority {validator.priority}")
    
    def remove_validator(self, validator_name: str) -> None:
        """Removes a validator from the pipeline"""
        self._validators = [v for v in self._validators if v.validator_name != validator_name]
        logger.info(f"Removed validator {validator_name}")
    
    def execute(self, event_data: Dict[str, Any], context: ValidationContext) -> AggregatedValidationResult:
        """
        Executes the complete validation pipeline
        """
        start_time = time.time()
        validator_results = []
        total_errors = 0
        total_warnings = 0
        
        logger.info(f"Starting validation pipeline with {len(self._validators)} validators")
        
        # Create factory config from context
        factory_config = FactoryConfiguration(
            factory_id=context.factory_id,
            factory_name=context.factory_id,  # Simple default
            validation_config=context.configuration
        )
        
        for validator in self._validators:
            try:
                # Check if validator applies to this event type and factory
                if not validator.can_validate(context.event_type, factory_config):
                    logger.debug(f"Skipping validator {validator.validator_name} - not applicable")
                    continue
                
                logger.debug(f"Executing validator {validator.validator_name}")
                validator_start = time.time()
                
                result = validator.validate(event_data, context)
                result.execution_time_ms = (time.time() - validator_start) * 1000
                
                validator_results.append(result)
                total_errors += len(result.errors)
                total_warnings += len(result.warnings)
                
                logger.debug(f"Validator {validator.validator_name} completed: "
                           f"valid={result.is_valid}, errors={len(result.errors)}, "
                           f"warnings={len(result.warnings)}")
                
                # Early exit on critical errors if configured
                if not result.is_valid and context.configuration.get('early_exit_on_error', False):
                    logger.info("Early exit triggered due to validation error")
                    break
                    
            except Exception as e:
                logger.error(f"Error in validator {validator.validator_name}: {str(e)}", exc_info=True)
                # Create error result for failed validator
                error_result = ValidationResult(
                    validator_name=validator.validator_name,
                    is_valid=False,
                    errors=[ValidationError(
                        field="validator_error",
                        message=f"Validator {validator.validator_name} failed: {str(e)}",
                        code="VALIDATOR_ERROR",
                        severity=ValidationSeverity.ERROR
                    )]
                )
                validator_results.append(error_result)
                total_errors += 1
        
        # Determine overall validity
        is_valid = total_errors == 0
        execution_time = (time.time() - start_time) * 1000
        
        logger.info(f"Validation pipeline completed: valid={is_valid}, "
                   f"errors={total_errors}, warnings={total_warnings}, "
                   f"time={execution_time:.2f}ms")
        
        return AggregatedValidationResult(
            is_valid=is_valid,
            validator_results=validator_results,
            total_errors=total_errors,
            total_warnings=total_warnings,
            execution_time_ms=execution_time
        )


class SchemaValidator(IEventValidator):
    """
    Validates event data against basic schema requirements.
    Checks for required fields and basic data types.
    """
    
    @property
    def validator_name(self) -> str:
        return "schema_validator"
    
    @property
    def priority(self) -> int:
        return 10  # Run first
    
    def can_validate(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Schema validation applies to all events"""
        return True
    
    def validate(self, event_data: Dict[str, Any], context: ValidationContext) -> ValidationResult:
        """
        Validates basic schema requirements
        """
        errors = []
        warnings = []
        
        # Required fields check
        required_fields = ['form', 'serial_number', 'event_data', 'timestamp']
        for field in required_fields:
            if field not in event_data or event_data[field] is None:
                errors.append(ValidationError(
                    field=field,
                    message=f"Required field '{field}' is missing or null",
                    code="REQUIRED_FIELD_MISSING",
                    value=event_data.get(field)
                ))
        
        # Basic type validation
        if 'form' in event_data and not isinstance(event_data['form'], (int, str)):
            errors.append(ValidationError(
                field='form',
                message="Field 'form' must be an integer or string",
                code="INVALID_TYPE",
                value=event_data['form']
            ))
        
        if 'serial_number' in event_data and not isinstance(event_data['serial_number'], str):
            errors.append(ValidationError(
                field='serial_number',
                message="Field 'serial_number' must be a string",
                code="INVALID_TYPE",
                value=event_data['serial_number']
            ))
        
        if 'event_data' in event_data and not isinstance(event_data['event_data'], dict):
            errors.append(ValidationError(
                field='event_data',
                message="Field 'event_data' must be a dictionary",
                code="INVALID_TYPE",
                value=type(event_data['event_data']).__name__
            ))
        
        # Check for empty serial number
        if event_data.get('serial_number') == '':
            errors.append(ValidationError(
                field='serial_number',
                message="Serial number cannot be empty",
                code="EMPTY_VALUE",
                value=event_data['serial_number']
            ))
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            validator_name=self.validator_name,
            is_valid=is_valid,
            errors=errors,
            warnings=warnings
        )


class BusinessRuleValidator(IEventValidator):
    """
    Validates event data against basic business rules.
    Can be extended with factory-specific rules.
    """

    @property
    def validator_name(self) -> str:
        return "business_rule_validator"

    @property
    def priority(self) -> int:
        return 20  # Run after schema validation

    def can_validate(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Business rule validation applies to all events"""
        return True

    def validate(self, event_data: Dict[str, Any], context: ValidationContext) -> ValidationResult:
        """
        Validates basic business rules
        """
        errors = []
        warnings = []

        # Check inspection status consistency
        inspection_status = event_data.get('inspection_status', True)
        next_action = event_data.get('next_action', 'main_forward')

        if inspection_status is False and next_action not in ['send_rework', 'corrected']:
            warnings.append(ValidationError(
                field='next_action',
                message="Failed inspection should typically result in rework or correction",
                code="INCONSISTENT_STATUS",
                severity=ValidationSeverity.WARNING,
                value=next_action
            ))

        # Check event_data content
        event_data_content = event_data.get('event_data', {})
        if isinstance(event_data_content, dict) and len(event_data_content) == 0:
            warnings.append(ValidationError(
                field='event_data',
                message="Event data is empty - this may indicate missing form data",
                code="EMPTY_EVENT_DATA",
                severity=ValidationSeverity.WARNING
            ))

        # Validate timestamp format (basic check)
        timestamp = event_data.get('timestamp')
        if timestamp and isinstance(timestamp, str):
            try:
                from datetime import datetime
                # Try to parse ISO format
                datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except ValueError:
                errors.append(ValidationError(
                    field='timestamp',
                    message="Timestamp must be in ISO format",
                    code="INVALID_TIMESTAMP_FORMAT",
                    value=timestamp
                ))

        is_valid = len(errors) == 0

        return ValidationResult(
            validator_name=self.validator_name,
            is_valid=is_valid,
            errors=errors,
            warnings=warnings
        )


class FIFOValidator(IEventValidator):
    """
    Validates FIFO (First-In-First-Out) compliance for manufacturing processes.
    This is a pure validator - it only validates, doesn't create violation logs.
    """

    @property
    def validator_name(self) -> str:
        return "fifo_validator"

    @property
    def priority(self) -> int:
        return 30  # Run after schema and business rule validation

    def can_validate(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """FIFO validation applies to all events that have serial numbers"""
        return True

    def validate(self, event_data: Dict[str, Any], context: ValidationContext) -> ValidationResult:
        """
        Validates FIFO compliance using the existing FIFO service logic
        """
        errors = []
        warnings = []

        try:
            # Import here to avoid circular imports
            from operation.services.fifo_validation_service import validate_event_with_fifo

            # Use the existing FIFO validation logic
            is_valid, fifo_message = validate_event_with_fifo(event_data)

            if not is_valid:
                # This is a blocking FIFO violation
                errors.append(ValidationError(
                    field='serial_number',
                    message=fifo_message or "FIFO validation failed",
                    code="FIFO_VIOLATION_BLOCKING",
                    severity=ValidationSeverity.ERROR,
                    value=event_data.get('serial_number')
                ))
            elif fifo_message:
                # This is a warning (violation allowed to proceed)
                warnings.append(ValidationError(
                    field='serial_number',
                    message=fifo_message,
                    code="FIFO_VIOLATION_WARNING",
                    severity=ValidationSeverity.WARNING,
                    value=event_data.get('serial_number')
                ))

            validation_is_valid = len(errors) == 0

            return ValidationResult(
                validator_name=self.validator_name,
                is_valid=validation_is_valid,
                errors=errors,
                warnings=warnings,
                metadata={'fifo_message': fifo_message, 'fifo_valid': is_valid}
            )

        except Exception as e:
            logger.error(f"Error in FIFO validation: {str(e)}", exc_info=True)
            return ValidationResult(
                validator_name=self.validator_name,
                is_valid=False,
                errors=[ValidationError(
                    field="fifo_validation",
                    message=f"FIFO validation failed: {str(e)}",
                    code="FIFO_VALIDATION_ERROR",
                    severity=ValidationSeverity.ERROR
                )]
            )


class RoutingValidator(IEventValidator):
    """
    Validates routing compliance for manufacturing processes.
    This is a pure validator - it only validates, doesn't update routing execution.
    """

    @property
    def validator_name(self) -> str:
        return "routing_validator"

    @property
    def priority(self) -> int:
        return 40  # Run after FIFO validation

    def can_validate(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Routing validation applies to all events"""
        return True

    def validate(self, event_data: Dict[str, Any], context: ValidationContext) -> ValidationResult:
        """
        Validates routing compliance using the existing routing validation service logic
        """
        errors = []
        warnings = []

        try:
            # Import here to avoid circular imports
            from workflow_config.services.routing_validation_service import RoutingValidationService

            # Use the existing routing validation logic
            is_valid, validation_errors, next_executable = RoutingValidationService.validate_event(event_data)

            # Convert validation errors to our format
            for error in validation_errors or []:
                errors.append(ValidationError(
                    field=error.get('field', 'routing'),
                    message=error.get('error', 'Routing validation failed'),
                    code="ROUTING_VALIDATION_ERROR",
                    severity=ValidationSeverity.ERROR,
                    value=error.get('value')
                ))

            return ValidationResult(
                validator_name=self.validator_name,
                is_valid=is_valid,
                errors=errors,
                warnings=warnings,
                metadata={
                    'next_executable': next_executable,
                    'routing_validation_errors': validation_errors
                }
            )

        except Exception as e:
            logger.error(f"Error in routing validation: {str(e)}", exc_info=True)
            return ValidationResult(
                validator_name=self.validator_name,
                is_valid=False,
                errors=[ValidationError(
                    field="routing_validation",
                    message=f"Routing validation failed: {str(e)}",
                    code="ROUTING_VALIDATION_ERROR",
                    severity=ValidationSeverity.ERROR
                )]
            )

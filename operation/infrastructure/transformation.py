"""
Event transformation services for the new event creation system.
"""
import logging
from typing import Dict, List, Any
from ..domain.interfaces import IEventTransformer, IEventTransformationService
from ..domain.models import (
    TransformationResult, TransformationContext, ValidationError,
    ValidationSeverity, FactoryConfiguration
)

logger = logging.getLogger(__name__)


class EventTransformationService(IEventTransformationService):
    """
    Service that manages event transformation using registered transformers.
    """
    
    def __init__(self):
        self._transformers: List[IEventTransformer] = []
    
    def add_transformer(self, transformer: IEventTransformer) -> None:
        """Adds a transformer to the service"""
        self._transformers.append(transformer)
        logger.info(f"Added transformer {transformer.transformer_name}")
    
    def transform_event(self, event_data: Dict[str, Any], context: TransformationContext) -> TransformationResult:
        """
        Transforms event data using the appropriate transformer
        """
        logger.info(f"Starting event transformation for event type: {context.event_type}")
        
        # Create factory config from context
        factory_config = FactoryConfiguration(
            factory_id=context.factory_id,
            factory_name=context.factory_id,
            transformation_config=context.configuration
        )
        
        # Find appropriate transformer
        transformer = self._find_transformer(context.event_type, factory_config)
        
        if not transformer:
            logger.warning(f"No transformer found for event type {context.event_type}, using default")
            transformer = DefaultEventTransformer()
        
        try:
            result = transformer.transform(event_data, context)
            logger.info(f"Transformation completed: success={result.success}, "
                       f"events_count={len(result.transformed_events)}")
            return result
            
        except Exception as e:
            logger.error(f"Error during transformation: {str(e)}", exc_info=True)
            return TransformationResult(
                success=False,
                errors=[ValidationError(
                    field="transformation",
                    message=f"Transformation failed: {str(e)}",
                    code="TRANSFORMATION_ERROR",
                    severity=ValidationSeverity.ERROR
                )]
            )
    
    def _find_transformer(self, event_type: str, factory_config: FactoryConfiguration) -> IEventTransformer:
        """Finds the appropriate transformer for the event type and factory"""
        for transformer in self._transformers:
            if transformer.can_transform(event_type, factory_config):
                return transformer
        return None


class DefaultEventTransformer(IEventTransformer):
    """
    Default transformer that handles standard event transformation.
    """
    
    @property
    def transformer_name(self) -> str:
        return "default_transformer"
    
    def can_transform(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Default transformer can handle any event type"""
        return True
    
    def transform(self, event_data: Dict[str, Any], context: TransformationContext) -> TransformationResult:
        """
        Performs standard event transformation
        """
        try:
            # Process serial number if present
            serial_number = event_data.get('serial_number')
            if serial_number:
                processed_data = self._process_serial_number(serial_number)
                if not processed_data:
                    return TransformationResult(
                        success=False,
                        errors=[ValidationError(
                            field="serial_number",
                            message="Failed to process serial number",
                            code="SERIAL_NUMBER_PROCESSING_FAILED",
                            value=serial_number
                        )]
                    )
                
                # Enhance event data with processed information
                enhanced_event_data = {
                    **event_data,
                    'product': processed_data.get('product_id'),
                    'board': processed_data.get('board'),
                    'work_order': processed_data.get('work_order')
                }
            else:
                enhanced_event_data = event_data.copy()
            
            # Add default values if missing
            enhanced_event_data.setdefault('event_type', 'main')
            enhanced_event_data.setdefault('next_action', 'main_forward')
            enhanced_event_data.setdefault('inspection_status', True)
            enhanced_event_data.setdefault('validation_status', 'pending')
            enhanced_event_data.setdefault('validation_errors', [])
            
            # Add user context
            if context.user_context:
                enhanced_event_data['created_by'] = context.user_context.user_id
            
            # Add timestamp if missing
            if 'timestamp' not in enhanced_event_data:
                from datetime import datetime
                enhanced_event_data['timestamp'] = datetime.now().isoformat()
            
            return TransformationResult(
                success=True,
                transformed_events=[enhanced_event_data]
            )
            
        except Exception as e:
            logger.error(f"Error in default transformation: {str(e)}", exc_info=True)
            return TransformationResult(
                success=False,
                errors=[ValidationError(
                    field="transformation",
                    message=f"Default transformation failed: {str(e)}",
                    code="DEFAULT_TRANSFORMATION_ERROR"
                )]
            )
    
    def _process_serial_number(self, serial_number: str) -> Dict[str, Any]:
        """
        Processes serial number to extract product, board, and work order information.
        This is a simplified version - in practice, this would use the existing
        serial number parsing logic.
        """
        try:
            # Import the existing serial number parsing function
            from workflow_config.services.form_service import parse_serial_number
            from catalog.models import Product
            
            parsed_data = parse_serial_number(serial_number)
            
            # Get product
            try:
                product = Product.objects.get(code=parsed_data['part_code'])
                return {
                    'product_id': product.id,
                    'board': parsed_data.get('board', 1),
                    'work_order': parsed_data.get('work_order', ''),
                    'part_code': parsed_data['part_code']
                }
            except Product.DoesNotExist:
                logger.warning(f"Product not found for code: {parsed_data['part_code']}")
                return None
                
        except Exception as e:
            logger.error(f"Error processing serial number {serial_number}: {str(e)}")
            return None


class CorrectedEventTransformer(IEventTransformer):
    """
    Transformer for corrected events that creates both failed and success events.
    """
    
    @property
    def transformer_name(self) -> str:
        return "corrected_event_transformer"
    
    def can_transform(self, event_type: str, factory_config: FactoryConfiguration) -> bool:
        """Can transform events that are marked as corrected"""
        return event_type == "corrected" or event_type == "rework"
    
    def transform(self, event_data: Dict[str, Any], context: TransformationContext) -> TransformationResult:
        """
        Creates both failed and corrected events
        """
        try:
            # First, use default transformer to get base event
            default_transformer = DefaultEventTransformer()
            base_result = default_transformer.transform(event_data, context)
            
            if not base_result.success:
                return base_result
            
            base_event = base_result.transformed_events[0]
            
            # Check if this should create corrected events
            is_corrected = (
                base_event.get('inspection_status') is False and 
                base_event.get('next_action') == 'corrected'
            )
            
            if not is_corrected:
                return base_result
            
            # Create failed event
            failed_event = base_event.copy()
            failed_event['inspection_status'] = False
            failed_event['next_action'] = 'corrected'
            
            # Create corrected event
            corrected_event = base_event.copy()
            corrected_event['inspection_status'] = True
            corrected_event['next_action'] = 'main_forward'
            corrected_event['event_type'] = 'rework'
            
            # Update event_data for corrected event
            corrected_event_data = corrected_event.get('event_data', {}).copy()
            corrected_event_data['form_status'] = 'pass'
            corrected_event['event_data'] = corrected_event_data
            
            return TransformationResult(
                success=True,
                transformed_events=[failed_event, corrected_event],
                metadata={'event_count': 2, 'type': 'corrected_events'}
            )
            
        except Exception as e:
            logger.error(f"Error in corrected event transformation: {str(e)}", exc_info=True)
            return TransformationResult(
                success=False,
                errors=[ValidationError(
                    field="transformation",
                    message=f"Corrected event transformation failed: {str(e)}",
                    code="CORRECTED_TRANSFORMATION_ERROR"
                )]
            )

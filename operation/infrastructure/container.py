"""
Simple dependency injection container for the new event creation system.
"""
import logging
from typing import Type, TypeVar, Dict, Any, Callable, List
from ..domain.interfaces import (
    IValidationPipeline, IEventValidator, IEventTransformationService, 
    IEventTransformer, IEventProcessingService, IEventProcessor,
    IConfigurationService, IEventRepository
)
from .validation import ValidationPipeline, SchemaValidator, BusinessRuleValidator
from .transformation import EventTransformationService, DefaultEventTransformer, CorrectedEventTransformer
from .processing import EventProcessingService, PersistenceProcessor, RoutingUpdateProcessor
from .configuration import ConfigurationService
from .repository import EventRepository

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceContainer:
    """
    Simple dependency injection container.
    Manages service registration and resolution.
    """
    
    def __init__(self):
        self._singletons: Dict[Type, Any] = {}
        self._transient_factories: Dict[Type, Callable[[], Any]] = {}
        self._singleton_factories: Dict[Type, Callable[[], Any]] = {}
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> None:
        """
        Registers a singleton service.
        """
        logger.debug(f"Registering singleton: {interface.__name__} -> {implementation.__name__}")
        self._singleton_factories[interface] = lambda: implementation()
    
    def register_singleton_instance(self, interface: Type[T], instance: T) -> None:
        """
        Registers a singleton instance.
        """
        logger.debug(f"Registering singleton instance: {interface.__name__}")
        self._singletons[interface] = instance
    
    def register_transient(self, interface: Type[T], implementation: Type[T]) -> None:
        """
        Registers a transient service (new instance each time).
        """
        logger.debug(f"Registering transient: {interface.__name__} -> {implementation.__name__}")
        self._transient_factories[interface] = lambda: implementation()
    
    def register_factory(self, interface: Type[T], factory: Callable[[], T]) -> None:
        """
        Registers a factory function for creating services.
        """
        logger.debug(f"Registering factory: {interface.__name__}")
        self._transient_factories[interface] = factory
    
    def resolve(self, interface: Type[T]) -> T:
        """
        Resolves a service instance.
        """
        # Check for existing singleton
        if interface in self._singletons:
            return self._singletons[interface]
        
        # Check for singleton factory
        if interface in self._singleton_factories:
            instance = self._singleton_factories[interface]()
            self._singletons[interface] = instance
            return instance
        
        # Check for transient factory
        if interface in self._transient_factories:
            return self._transient_factories[interface]()
        
        raise ValueError(f"Service not registered: {interface.__name__}")
    
    def resolve_all(self, interface: Type[T]) -> List[T]:
        """
        Resolves all implementations of an interface.
        This is a simplified version - in practice would track multiple registrations.
        """
        try:
            return [self.resolve(interface)]
        except ValueError:
            return []
    
    def is_registered(self, interface: Type[T]) -> bool:
        """
        Checks if a service is registered.
        """
        return (interface in self._singletons or 
                interface in self._singleton_factories or 
                interface in self._transient_factories)


def configure_container(factory_id: str = "default") -> ServiceContainer:
    """
    Configures the dependency injection container with default services.
    """
    container = ServiceContainer()
    
    logger.info(f"Configuring service container for factory: {factory_id}")
    
    # Configuration Service (singleton)
    container.register_singleton(IConfigurationService, ConfigurationService)
    
    # Repository (singleton)
    container.register_singleton(IEventRepository, EventRepository)
    
    # Validation Pipeline (transient)
    def create_validation_pipeline():
        pipeline = ValidationPipeline()
        pipeline.add_validator(SchemaValidator())
        pipeline.add_validator(BusinessRuleValidator())
        return pipeline
    
    container.register_factory(IValidationPipeline, create_validation_pipeline)
    
    # Transformation Service (singleton)
    def create_transformation_service():
        service = EventTransformationService()
        service.add_transformer(DefaultEventTransformer())
        service.add_transformer(CorrectedEventTransformer())
        return service
    
    container.register_factory(IEventTransformationService, create_transformation_service)
    
    # Processing Service (singleton)
    def create_processing_service():
        repository = container.resolve(IEventRepository)
        service = EventProcessingService(repository)
        service.add_processor(PersistenceProcessor(repository))
        service.add_processor(RoutingUpdateProcessor(repository))
        return service
    
    container.register_factory(IEventProcessingService, create_processing_service)
    
    logger.info("Service container configuration completed")
    return container


def configure_test_container() -> ServiceContainer:
    """
    Configures a container for testing with mock services.
    """
    from .repository import MockEventRepository
    
    container = ServiceContainer()
    
    logger.info("Configuring test service container")
    
    # Use mock repository for testing
    container.register_singleton(IEventRepository, MockEventRepository)
    container.register_singleton(IConfigurationService, ConfigurationService)
    
    # Validation Pipeline
    def create_validation_pipeline():
        pipeline = ValidationPipeline()
        pipeline.add_validator(SchemaValidator())
        pipeline.add_validator(BusinessRuleValidator())
        return pipeline
    
    container.register_factory(IValidationPipeline, create_validation_pipeline)
    
    # Transformation Service
    def create_transformation_service():
        service = EventTransformationService()
        service.add_transformer(DefaultEventTransformer())
        return service
    
    container.register_factory(IEventTransformationService, create_transformation_service)
    
    # Processing Service
    def create_processing_service():
        repository = container.resolve(IEventRepository)
        service = EventProcessingService(repository)
        service.add_processor(PersistenceProcessor(repository))
        return service
    
    container.register_factory(IEventProcessingService, create_processing_service)
    
    logger.info("Test service container configuration completed")
    return container


# Global container instance
_container = None


def get_container() -> ServiceContainer:
    """
    Gets the global service container instance.
    """
    global _container
    if _container is None:
        _container = configure_container()
    return _container


def set_container(container: ServiceContainer) -> None:
    """
    Sets the global service container instance.
    Useful for testing or custom configurations.
    """
    global _container
    _container = container


def reset_container() -> None:
    """
    Resets the global container.
    Useful for testing.
    """
    global _container
    _container = None

"""
Repository implementations for the new event creation system.
"""
import logging
from typing import Dict, List, Any
from django.db import transaction
from django.core.exceptions import ValidationError as DjangoValidationError
from ..domain.interfaces import IEventRepository
from ..models import ManufacturingEvent
from workflow_config.models import RoutingExecution
from workflow_config.services.routing_validation_service import RoutingValidationService

logger = logging.getLogger(__name__)


class EventRepository(IEventRepository):
    """
    Repository for managing event persistence and related operations.
    """
    
    def __init__(self):
        self.routing_service = RoutingValidationService()
    
    def save_events(self, events: List[Dict[str, Any]]) -> List[ManufacturingEvent]:
        """
        Saves events to the database using Django ORM.
        """
        logger.info(f"Saving {len(events)} events to database")
        
        created_events = []
        
        try:
            with transaction.atomic():
                for event_data in events:
                    try:
                        # Create ManufacturingEvent instance
                        event = self._create_event_from_data(event_data)
                        event.full_clean()  # Validate model
                        event.save()
                        created_events.append(event)
                        
                        logger.debug(f"Created event {event.id} for serial {event.serial_number}")
                        
                    except (DjangoValidationError, Exception) as e:
                        logger.error(f"Error creating event from data {event_data}: {str(e)}")
                        raise  # Re-raise to trigger transaction rollback
                
                logger.info(f"Successfully saved {len(created_events)} events")
                return created_events
                
        except Exception as e:
            logger.error(f"Error saving events: {str(e)}", exc_info=True)
            raise
    
    def update_routing_execution(self, serial_number: str, event_ids: List[int]) -> bool:
        """
        Updates routing execution with event IDs.
        """
        try:
            logger.debug(f"Updating routing execution for serial {serial_number} with events {event_ids}")
            
            # Get the events
            events = ManufacturingEvent.objects.filter(id__in=event_ids)
            if not events.exists():
                logger.warning(f"No events found for IDs: {event_ids}")
                return False
            
            # Use the existing routing validation service
            for event in events:
                try:
                    self.routing_service.update_routing_execution_with_event_ids(
                        serial_number, [event.id]
                    )
                except Exception as e:
                    logger.error(f"Error updating routing for event {event.id}: {str(e)}")
                    # Continue with other events rather than failing completely
            
            logger.debug(f"Routing execution updated for serial {serial_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating routing execution: {str(e)}", exc_info=True)
            return False
    
    def _create_event_from_data(self, event_data: Dict[str, Any]) -> ManufacturingEvent:
        """
        Creates a ManufacturingEvent instance from event data.
        """
        # Extract required fields
        serial_number = event_data.get('serial_number')
        form_id = event_data.get('form')
        timestamp = event_data.get('timestamp')
        
        # Handle timestamp conversion
        if isinstance(timestamp, str):
            from datetime import datetime
            try:
                # Try to parse ISO format
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except ValueError:
                # Fall back to current time if parsing fails
                timestamp = datetime.now()
        
        # Create the event
        event = ManufacturingEvent(
            serial_number=serial_number,
            form_id=form_id,
            timestamp=timestamp,
            product_id=event_data.get('product'),
            board=event_data.get('board', 1),
            work_order=event_data.get('work_order', ''),
            event_type=event_data.get('event_type', 'main'),
            next_action=event_data.get('next_action', 'main_forward'),
            inspection_status=event_data.get('inspection_status', True),
            validation_status=event_data.get('validation_status', 'pending'),
            validation_errors=event_data.get('validation_errors', []),
            event_data=event_data.get('event_data', {}),
            created_by_id=event_data.get('created_by')
        )
        
        return event
    
    def get_events_by_serial(self, serial_number: str) -> List[ManufacturingEvent]:
        """
        Retrieves all events for a given serial number.
        """
        return list(ManufacturingEvent.objects.filter(
            serial_number=serial_number
        ).order_by('timestamp'))
    
    def get_event_by_id(self, event_id: int) -> ManufacturingEvent:
        """
        Retrieves a single event by ID.
        """
        try:
            return ManufacturingEvent.objects.get(id=event_id)
        except ManufacturingEvent.DoesNotExist:
            logger.warning(f"Event with ID {event_id} not found")
            return None
    
    def update_event_validation_status(self, event_id: int, status: str, errors: List[str] = None) -> bool:
        """
        Updates the validation status of an event.
        """
        try:
            event = ManufacturingEvent.objects.get(id=event_id)
            event.validation_status = status
            if errors:
                event.validation_errors = errors
            event.save(update_fields=['validation_status', 'validation_errors'])
            
            logger.debug(f"Updated validation status for event {event_id} to {status}")
            return True
            
        except ManufacturingEvent.DoesNotExist:
            logger.warning(f"Event with ID {event_id} not found for validation update")
            return False
        except Exception as e:
            logger.error(f"Error updating validation status for event {event_id}: {str(e)}")
            return False


class MockEventRepository(IEventRepository):
    """
    Mock repository for testing purposes.
    Stores events in memory instead of database.
    """
    
    def __init__(self):
        self._events = []
        self._next_id = 1
    
    def save_events(self, events: List[Dict[str, Any]]) -> List[Any]:
        """
        Saves events to memory.
        """
        created_events = []
        
        for event_data in events:
            # Create a mock event object
            mock_event = type('MockEvent', (), {
                'id': self._next_id,
                'serial_number': event_data.get('serial_number'),
                'form_id': event_data.get('form'),
                'timestamp': event_data.get('timestamp'),
                'event_type': event_data.get('event_type', 'main'),
                'event_data': event_data
            })()
            
            self._events.append(mock_event)
            created_events.append(mock_event)
            self._next_id += 1
        
        logger.info(f"Mock repository saved {len(created_events)} events")
        return created_events
    
    def update_routing_execution(self, serial_number: str, event_ids: List[int]) -> bool:
        """
        Mock routing execution update.
        """
        logger.info(f"Mock routing update for serial {serial_number} with events {event_ids}")
        return True
    
    def get_all_events(self) -> List[Any]:
        """
        Returns all stored events (for testing).
        """
        return self._events.copy()
    
    def clear_events(self) -> None:
        """
        Clears all stored events (for testing).
        """
        self._events.clear()
        self._next_id = 1

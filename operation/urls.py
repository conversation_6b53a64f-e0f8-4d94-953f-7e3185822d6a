from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views.event_views import ManufacturingEventViewSet
from .views.event_views_v2 import ManufacturingEventV2ViewSet
from .views.work_order_views import WorkOrderViewSet, WorkOrderExcelUploadView
from .views.fifo_views import FIFOViolationLogViewSet

# Create a router and register our viewsets
router = DefaultRouter()
router.register(r'events', ManufacturingEventViewSet, basename='event')
router.register(r'v2/events', ManufacturingEventV2ViewSet, basename='events-v2')
router.register(r'work-orders', WorkOrderViewSet, basename='work-order')
router.register(r'fifo-violations', FIFOViolationLogViewSet, basename='fifo-violation')

urlpatterns = [
    path('', include(router.urls)),
    path('import-wo-excel/', WorkOrderExcelUploadView.as_view(), name='work-order-import-excel'),
]

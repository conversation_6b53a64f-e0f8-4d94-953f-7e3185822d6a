"""
Command definitions for the new event creation system.
Implements the Command Pattern for better separation of concerns.
"""
from dataclasses import dataclass, field
from typing import Dict, Any
import uuid
from ..domain.models import UserContext, RequestMetadata


@dataclass
class CreateManufacturingEventCommand:
    """
    Command for creating manufacturing events.
    Contains all data and context needed for event creation.
    """
    # Core event data
    event_data: Dict[str, Any]
    
    # Context information
    factory_id: str
    user_context: UserContext
    request_metadata: RequestMetadata
    
    # Processing options
    validation_mode: str = "strict"  # strict, lenient
    skip_fifo_validation: bool = False
    dry_run: bool = False
    
    # Tracing and correlation
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    parent_span_id: str = None
    
    def __post_init__(self):
        """Validate command data after initialization"""
        if not self.event_data:
            raise ValueError("event_data cannot be empty")
        if not self.factory_id:
            raise ValueError("factory_id is required")
        if not self.user_context:
            raise ValueError("user_context is required")


@dataclass
class ValidateEventCommand:
    """
    Command for validating events without creating them.
    Useful for pre-validation and testing.
    """
    # Core event data
    event_data: Dict[str, Any]
    
    # Context information
    factory_id: str
    user_context: UserContext
    
    # Validation options
    validation_mode: str = "strict"
    include_warnings: bool = True
    
    # Tracing
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    
    def __post_init__(self):
        """Validate command data after initialization"""
        if not self.event_data:
            raise ValueError("event_data cannot be empty")
        if not self.factory_id:
            raise ValueError("factory_id is required")

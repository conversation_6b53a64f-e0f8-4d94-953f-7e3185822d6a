"""
Command handlers for the new event creation system.
Implements the application layer logic for processing commands.
"""
import time
import logging
from typing import Dict, Any
from ..domain.interfaces import (
    IValidationPipeline, IEventTransformationService, 
    IEventProcessingService, IConfigurationService
)
from ..domain.models import (
    EventCreationResult, ValidationContext, TransformationContext, 
    ProcessingContext, AggregatedValidationResult, ValidationSeverity
)
from .commands import CreateManufacturingEventCommand, ValidateEventCommand

logger = logging.getLogger(__name__)


class CreateManufacturingEventCommandHandler:
    """
    Handles the CreateManufacturingEventCommand.
    Orchestrates the entire event creation process.
    """
    
    def __init__(
        self,
        validation_pipeline: IValidationPipeline,
        transformation_service: IEventTransformationService,
        processing_service: IEventProcessingService,
        configuration_service: IConfigurationService
    ):
        self.validation_pipeline = validation_pipeline
        self.transformation_service = transformation_service
        self.processing_service = processing_service
        self.configuration_service = configuration_service
    
    def handle(self, command: CreateManufacturingEventCommand) -> EventCreationResult:
        """
        Handles the event creation command.
        
        Args:
            command: The command to process
            
        Returns:
            EventCreationResult with the outcome of the operation
        """
        start_time = time.time()
        
        try:
            logger.info(f"Processing event creation command {command.correlation_id}")
            
            # Get factory configuration
            factory_config = self.configuration_service.get_factory_configuration(command.factory_id)
            
            # Step 1: Validation
            validation_context = ValidationContext(
                factory_id=command.factory_id,
                event_type=command.event_data.get('event_type', 'unknown'),
                user_context=command.user_context,
                request_metadata=command.request_metadata,
                configuration=factory_config.validation_config
            )
            
            validation_result = self.validation_pipeline.execute(
                command.event_data, 
                validation_context
            )
            
            # Check if validation failed and we're in strict mode
            if not validation_result.is_valid and command.validation_mode == "strict":
                logger.warning(f"Validation failed for command {command.correlation_id}")
                return EventCreationResult(
                    success=False,
                    validation_result=validation_result,
                    correlation_id=command.correlation_id,
                    total_execution_time_ms=(time.time() - start_time) * 1000
                )
            
            # Step 2: Transformation
            transformation_context = TransformationContext(
                factory_id=command.factory_id,
                event_type=command.event_data.get('event_type', 'unknown'),
                user_context=command.user_context,
                validation_result=validation_result,
                configuration=factory_config.transformation_config
            )
            
            transformation_result = self.transformation_service.transform_event(
                command.event_data,
                transformation_context
            )
            
            if not transformation_result.success:
                logger.error(f"Transformation failed for command {command.correlation_id}")
                return EventCreationResult(
                    success=False,
                    validation_result=validation_result,
                    transformation_result=transformation_result,
                    correlation_id=command.correlation_id,
                    total_execution_time_ms=(time.time() - start_time) * 1000
                )
            
            # Step 3: Processing (if not dry run)
            if command.dry_run:
                logger.info(f"Dry run completed for command {command.correlation_id}")
                return EventCreationResult(
                    success=True,
                    validation_result=validation_result,
                    transformation_result=transformation_result,
                    correlation_id=command.correlation_id,
                    total_execution_time_ms=(time.time() - start_time) * 1000
                )
            
            processing_context = ProcessingContext(
                factory_id=command.factory_id,
                user_context=command.user_context,
                validation_result=validation_result,
                transformation_result=transformation_result,
                configuration=factory_config.processing_config,
                metadata={}
            )
            
            processing_result = self.processing_service.process_events(
                transformation_result.transformed_events,
                processing_context
            )
            
            # Final result
            success = processing_result.status.value == "success"
            total_time = (time.time() - start_time) * 1000
            
            logger.info(f"Event creation completed for command {command.correlation_id}, success: {success}")
            
            return EventCreationResult(
                success=success,
                events=processing_result.created_events,
                validation_result=validation_result,
                transformation_result=transformation_result,
                processing_result=processing_result,
                correlation_id=command.correlation_id,
                total_execution_time_ms=total_time
            )
            
        except Exception as e:
            logger.error(f"Error processing command {command.correlation_id}: {str(e)}", exc_info=True)
            return EventCreationResult(
                success=False,
                correlation_id=command.correlation_id,
                total_execution_time_ms=(time.time() - start_time) * 1000
            )


class ValidateEventCommandHandler:
    """
    Handles the ValidateEventCommand.
    Performs validation without creating events.
    """
    
    def __init__(
        self,
        validation_pipeline: IValidationPipeline,
        configuration_service: IConfigurationService
    ):
        self.validation_pipeline = validation_pipeline
        self.configuration_service = configuration_service
    
    def handle(self, command: ValidateEventCommand) -> AggregatedValidationResult:
        """
        Handles the event validation command.
        
        Args:
            command: The validation command to process
            
        Returns:
            AggregatedValidationResult with validation outcome
        """
        try:
            logger.info(f"Processing event validation command {command.correlation_id}")
            
            # Get factory configuration
            factory_config = self.configuration_service.get_factory_configuration(command.factory_id)
            
            # Create validation context
            validation_context = ValidationContext(
                factory_id=command.factory_id,
                event_type=command.event_data.get('event_type', 'unknown'),
                user_context=command.user_context,
                request_metadata=None,  # Not needed for validation-only
                configuration=factory_config.validation_config
            )
            
            # Execute validation pipeline
            result = self.validation_pipeline.execute(command.event_data, validation_context)
            
            logger.info(f"Validation completed for command {command.correlation_id}, valid: {result.is_valid}")
            return result
            
        except Exception as e:
            logger.error(f"Error validating command {command.correlation_id}: {str(e)}", exc_info=True)
            # Return a failed validation result
            return AggregatedValidationResult(
                is_valid=False,
                total_errors=1,
                execution_time_ms=0.0
            )

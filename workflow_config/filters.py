import django_filters
from workflow_config.models import Routing, FormConfig


class RoutingFilter(django_filters.FilterSet):
    """
    Filter for Routing model
    """
    routing_code = django_filters.CharFilter(field_name='code', lookup_expr='icontains')
    form_code = django_filters.CharFilter(method='filter_by_form_code')
    product_code = django_filters.CharFilter(field_name='products__code', lookup_expr='iexact')

    class Meta:
        model = Routing
        fields = ['routing_code', 'form_code', 'product_code']

    def filter_by_form_code(self, queryset, name, value):
        """
        Filter routings by form code

        This method finds all forms with the given code, then finds their
        associated process blocks, and finally returns all routings that
        include those process blocks in their schema.
        """
        if not value:
            return queryset

        # Find forms with the given code
        forms = FormConfig.objects.filter(code__icontains=value)

        # Get process blocks associated with these forms
        process_blocks = [form.process_block.code for form in forms if form.process_block]

        if not process_blocks:
            return queryset.none()

        # Find routings that include these process blocks in their schema
        filtered_routings = []
        for routing in queryset:
            routing_schema = routing.schema.get('routing_schema', {})
            components = routing_schema.get('components', {})

            # Check if any of the process blocks are in the routing components
            if any(pb in components for pb in process_blocks):
                filtered_routings.append(routing.id)

        return queryset.filter(id__in=filtered_routings)

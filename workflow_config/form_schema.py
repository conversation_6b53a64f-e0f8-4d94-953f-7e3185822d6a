import re
from typing import Any, Dict
from jsonschema import validate
from jsonschema.exceptions import ValidationError as JsonSchemaError
from rest_framework.exceptions import ValidationError

# JSON Schema for new form + workflows
NEW_FORM_SCHEMA: Dict[str, Any] = {
    "type": "object",
    "properties": {
        "id": {"type": "string"},
        "title": {"type": "string"},
        "fields": {
            "type": "array",
            "items": {"$ref": "#/definitions/FieldSchema"}
        },
        "workflows": {
            "type": "array",
            "items": {"$ref": "#/definitions/Workflow"}
        }
    },
    "required": ["id", "title", "fields"],
    "additionalProperties": False,
    "definitions": {
        "FieldSchema": {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"},
                "label": {"type": "string"},
                "placeholder": {"type": "string"},
                "description": {"type": "string"},
                "type": {"enum": ["text","number","checkbox","select","multiselect","date","time","datetime","break"]},
                "required": {"type": "boolean"},
                "options": {
                    "type": "array",
                    "items": {"anyOf": [
                        {"type": "string"},
                        {"type": "object",
                         "properties": {
                             "value": {"type": "string"},
                             "label": {"type": "string"},
                             "hideFields": {"type": "array", "items": {"type": "string"}}
                         },
                         "required": ["value", "label"],
                         "additionalProperties": False
                        }
                    ]}
                },
                "validation": {
                    "type": "object",
                    "properties": {
                        "pattern": {"type": "string"},
                        "message": {"type": "string"}
                    },
                    "additionalProperties": False
                },
                "condition": {"$ref": "#/definitions/Condition"},
                "dependencies": {
                    "type": "object",
                    "properties": {
                        "field": {"type": "string"},
                        "operator": {"enum": ["==","!=",">","<",">=","<="]},
                        "value": {},
                        "validations": {
                            "type": "array",
                            "items": {"$ref": "#/definitions/ValidationRule"}
                        }
                    },
                    "required": ["field", "operator", "value"],
                    "additionalProperties": False
                },
                "asyncOptionsConfig": {
                    "type": "object",
                    "properties": {"identifier": {"type": "string"}},
                    "required": ["identifier"],
                    "additionalProperties": False
                },
                "defaultValue": {},
                "layout": {
                    "type": "object",
                    "properties": {
                        "xs": {"type": "integer"},
                        "sm": {"type": "integer"},
                        "md": {"type": "integer"},
                        "lg": {"type": "integer"},
                        "xl": {"type": "integer"},
                        "rowBreak": {"type": "boolean"}
                    },
                    "additionalProperties": False
                },
                "section": {"type": "string"}
            },
            "required": ["id", "name", "label", "type"],
            "additionalProperties": False
        },
        "Condition": {
            "type": "object",
            "properties": {
                "field": {"type": "string"},
                "operator": {"enum": ["==","!=",">","<",">=","<="]},
                "value": {}
            },
            "required": ["field", "operator", "value"],
            "additionalProperties": False
        },
        "ValidationRule": {
            "type": "object",
            "oneOf": [
                {
                    "properties": {"type": {"const": "comparison"}, "config": {"$ref": "#/definitions/ComparisonValidationConfig"}, "message": {"type": "string"}},
                    "required": ["type", "config"],
                    "additionalProperties": False
                },
                {
                    "properties": {"type": {"const": "allowedValues"}, "config": {"$ref": "#/definitions/AllowedValuesConfig"}, "message": {"type": "string"}},
                    "required": ["type", "config"],
                    "additionalProperties": False
                },
                {
                    "properties": {"type": {"const": "regexMatch"}, "config": {"$ref": "#/definitions/RegexMatchConfig"}, "message": {"type": "string"}},
                    "required": ["type", "config"],
                    "additionalProperties": False
                },
                {
                    "properties": {"type": {"const": "customScript"}, "config": {"$ref": "#/definitions/CustomScriptConfig"}, "message": {"type": "string"}},
                    "required": ["type", "config"],
                    "additionalProperties": False
                }
            ]
        },
        "ComparisonValidationConfig": {
            "type": "object",
            "properties": {
                "operator": {"enum": ["==","!=",">","<",">=","<="]},
                "value": {"anyOf": [{"type": "number"}, {"type": "string"}]}
            },
            "required": ["operator", "value"],
            "additionalProperties": False
        },
        "AllowedValuesConfig": {
            "type": "object",
            "properties": {"values": {"type": "array", "items": {"type": "string"}}},
            "required": ["values"],
            "additionalProperties": False
        },
        "RegexMatchConfig": {
            "type": "object",
            "properties": {"pattern": {"type": "string"}},
            "required": ["pattern"],
            "additionalProperties": False
        },
        "CustomScriptConfig": {
            "type": "object",
            "properties": {"script": {"type": "string"}},
            "required": ["script"],
            "additionalProperties": False
        },
        "Workflow": {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "formId": {"type": "string"},
                "name": {"type": "string"},
                "trigger": {"$ref": "#/definitions/WorkflowTrigger"},
                "conditions": {"type": "array", "items": {"$ref": "#/definitions/WorkflowCondition"}},
                "actions": {"type": "array", "items": {"$ref": "#/definitions/WorkflowAction"}}
            },
            "required": ["id", "formId", "name", "trigger", "actions"],
            "additionalProperties": False
        },
        "WorkflowTrigger": {
            "type": "object",
            "properties": {
                "type": {"enum": ["form","field","record"]},
                "event": {"type": "string"},
                "fieldName": {"type": "string"}
            },
            "required": ["type", "event"],
            "additionalProperties": False
        },
        "WorkflowCondition": {
            "type": "object",
            "properties": {"field": {"type": "string"}, "operator": {"enum": ["==","!=",">","<",">=","<="]}, "value": {}},
            "required": ["field", "operator", "value"],
            "additionalProperties": False
        },
        "WorkflowAction": {
            "type": "object",
            "oneOf": [
                {"properties": {"type": {"const": "api_call"}, "config": {"$ref": "#/definitions/ApiCallActionConfig"}}, "required": ["type", "config"], "additionalProperties": False},
                {"properties": {"type": {"const": "update_field"}, "config": {"$ref": "#/definitions/UpdateFieldActionConfig"}}, "required": ["type", "config"], "additionalProperties": False},
                {"properties": {"type": {"const": "script"}, "config": {"$ref": "#/definitions/ScriptActionConfig"}}, "required": ["type", "config"], "additionalProperties": False}
            ]
        },
        "ApiCallActionConfig": {
            "type": "object",
            "properties": {
                "url": {"type": "string"},
                "method": {"enum": ["GET","POST","PUT","DELETE"]},
                "headers": {"type": "object", "additionalProperties": {"type": "string"}},
                "body": {"type": "object"},
                "mapResponseToField": {"type": "string"}
            },
            "required": ["url", "method"],
            "additionalProperties": False
        },
        "UpdateFieldActionConfig": {
            "type": "object",
            "properties": {"field": {"type": "string"}, "value": {}},
            "required": ["field", "value"],
            "additionalProperties": False
        },
        "ScriptActionConfig": {
            "type": "object",
            "properties": {"code": {"type": "string"}},
            "required": ["code"],
            "additionalProperties": False
        },
    }
}

# Example schema demonstrating all NEW_FORM_SCHEMA features
EXAMPLE_SCHEMA = {
    "id": "start-production-job",
    "title": "Start Production Job",
    "fields": [
        {"id": "job_id", "name": "job_id", "label": "Job ID", "type": "text", "required": True,
         "validation": {"pattern": "^[A-Z0-9_-]{6,}$", "message": "Must be at least 6 chars"}},
        {"id": "operator_name", "name": "operator_name", "label": "Operator Name", "type": "text", "required": True,
         "defaultValue": "John Doe"},
        {"id": "shift", "name": "shift", "label": "Shift", "type": "select", "options": [
            "A", "B", {"value": "C", "label": "Shift C", "hideFields": ["supervisor_override"]}
        ]},
        {"id": "supervisor_override", "name": "supervisor_override", "label": "Supervisor Override", "type": "checkbox",
         "defaultValue": False},
        {"id": "machine_id", "name": "machine_id", "label": "Machine", "type": "select",
         "options": ["Machine 1", "Machine 2", "Machine 3"]},
        {"id": "start_time", "name": "start_time", "label": "Start Time", "type": "datetime",
         "defaultValue": "2025-04-24T00:00:00+00:00"},
        {"id": "estimated_output", "name": "estimated_output", "label": "Estimated Output", "type": "number",
         "condition": {"field": "supervisor_override", "operator": "==", "value": False},
        },
        {"id": "material_code", "name": "material_code", "label": "Material Code", "type": "text",
         "dependencies": {"field": "machine_id", "operator": "!=", "value": "", "validations": [
             {"type": "regexMatch", "config": {"pattern": "MAT-[0-9]{4}"}, "message": "Format MAT-XXXX"},
             {"type": "customScript", "config": {"script": "(val, ctx) => ctx.machine_id === 'Machine 1' ? val.startsWith('M1-') : true"},
              "message": "Prefix M1- required"}
         ]}},
        {"id": "confirm_conditions", "name": "confirm_conditions", "label": "Confirm Conditions", "type": "checkbox", "required": True}
    ],
    "workflows": [
        {"id": "populate-material", "formId": "start-production-job", "name": "Populate Material Code",
         "trigger": {"type": "field", "event": "onChange", "fieldName": "machine_id"},
         "actions": [{"type": "api_call", "config": {"url": "https://api.example.com/material?m={{machine_id}}",
             "method": "GET", "mapResponseToField": "material_code"}}]},
        {"id": "warn-output", "formId": "start-production-job", "name": "Warn High Output",
         "trigger": {"type": "field", "event": "onChange", "fieldName": "estimated_output"},
         "conditions": [{"field": "estimated_output", "operator": ">", "value": 1000}],
         "actions": [{"type": "script", "config": {"code": "alert('High output!')"}}]},
        {"id": "submit-job", "formId": "start-production-job", "name": "Submit Job",
         "trigger": {"type": "form", "event": "on_submit"},
         "actions": [{"type": "api_call", "config": {"url": "https://api.example.com/jobs/start", "method": "POST",
             "headers": {"Content-Type": "application/json"}, "body": {"job_id": "{{job_id}}"},
             "mapResponseToField": "supervisor_override"}}]},
        {"id": "clear-fields", "formId": "start-production-job", "name": "Clear Fields",
         "trigger": {"type": "form", "event": "after_submit"},
         "actions": [{"type": "update_field", "config": {"field": "operator_name", "value": ""}}]}
    ]
}

EXAMPLE_SCHEMA_V2 = {
  "id": "start-production-job",
  "title": "Start Production Job",
  "fields": [
    {
      "id": "job_id",
      "name": "job_id",
      "label": "Job ID",
      "type": "text",
      "required": True,
      "validation": {
        "pattern": "^[A-Z0-9_-]{6,}$",
        "message": "Job ID must be at least 6 characters and contain only A-Z, 0-9, _ or -"
      },
      "layout": { "md": 6 }
    },
    {
      "id": "operator_name",
      "name": "operator_name",
      "label": "Operator Name",
      "type": "text",
      "required": True,
      "layout": { "md": 6 }
    },
    {
      "id": "shift",
      "name": "shift",
      "label": "Shift",
      "type": "select",
      "required": True,
      "options": [
        { "value": "A", "label": "Shift A" },
        { "value": "B", "label": "Shift B" },
        { "value": "C", "label": "Shift C", "hideFields": ["supervisor_override"] }
      ],
      "layout": { "md": 4 }
    },
    {
      "id": "supervisor_override",
      "name": "supervisor_override",
      "label": "Supervisor Override",
      "type": "checkbox",
      "defaultValue": False,
      "layout": { "md": 4 }
    },
    {
      "id": "machine_id",
      "name": "machine_id",
      "label": "Machine",
      "type": "select",
      "required": True,
      "options":[
        "Machine 1",
        "Machine 2",
        "Machine 3"
      ],
      "layout": { "md": 4 }
    },
    {
      "id": "start_time",
      "name": "start_time",
      "label": "Start Time",
      "type": "datetime",
      "required": True,
      "defaultValue": "new Date()",
      "layout": { "md": 6 }
    },
    {
      "id": "estimated_output",
      "name": "estimated_output",
      "label": "Estimated Output (Units)",
      "type": "number",
      "required": True,
      "condition": {
        "field": "supervisor_override",
        "operator": "==",
        "value": False
      },
      "layout": { "md": 6 }
    },
    {
      "id": "material_code",
      "name": "material_code",
      "label": "Material Code",
      "type": "text",
      "dependencies": {
        "field": "machine_id",
        "operator": "!=",
        "value": "",
        "validations": [
          {
            "type": "regexMatch",
            "config": { "pattern": "MAT-[0-9]{4}" },
            "message": "Material code must follow MAT-XXXX format"
          }
        ]
      },
      "layout": { "md": 6 }
    },
    {
      "id": "confirm_conditions",
      "name": "confirm_conditions",
      "label": "I confirm all safety conditions are met.",
      "type": "checkbox",
      "required": True,
      "layout": { "md": 12 }
    }
  ],
  "workflows": [
    {
        "id": "populate-material-code",
        "formId": "start-production-job",
        "name": "Populate Material Code",
        "trigger": {
        "type": "field",
        "event": "onChange",
        "fieldName": "machine_id"
        },
        "actions": [
        {
            "type": "api_call",
            "config": {
            "url": "https://mes.api/fetch-material?machine_id={{machine_id}}",
            "method": "GET",
            "mapResponseToField": "material_code"
            }
        }
        ]
    },
    {
        "id": "warn-high-output",
        "formId": "start-production-job",
        "name": "Warn High Output",
        "trigger": {
            "type": "field",
            "event": "onChange",
            "fieldName": "estimated_output"
        },
        "conditions": [
            {
                "field": "estimated_output",
                "operator": ">",
                "value": 1000
            }
        ],
        "actions": [
            {
                "type": "script",
                "config": {
                    "code": "alert('Warning: Estimated output exceeds recommended limit!');"
                }
            }
        ]
    },
    {
        "id": "start-job-submission",
        "formId": "start-production-job",
        "name": "Submit Start Job",
        "trigger": {
        "type": "form",
        "event": "on_submit"
        },
        "actions": [
        {
            "type": "api_call",
            "config": {
            "url": "https://mes.api/jobs/start",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json"
            },
            "body": {
                "job_id": "{{job_id}}",
                "operator": "{{operator_name}}",
                "machine": "{{machine_id}}",
                "start_time": "{{start_time}}"
            }
            }
        }
        ]
    },
    {
        "id": "clear-sensitive-after-submit",
        "formId": "start-production-job",
        "name": "Clear Sensitive Info",
        "trigger": {
        "type": "form",
        "event": "after_submit"
        },
        "actions": [
        {
            "type": "update_field",
            "config": {
            "field": "operator_name",
            "value": ""
            }
        }
        ]
    }
  ]
}

def validate_new_form_schema(schema_data: Dict[str, Any]) -> None:
    """
    Validate form+workflow schema against NEW_FORM_SCHEMA.
    Raises ValidationError on failure.
    """
    try:
        validate(instance=schema_data, schema=NEW_FORM_SCHEMA)
    except JsonSchemaError as e:
        path = " -> ".join(str(p) for p in e.path)
        raise ValidationError(f"Invalid new form schema at {path}: {e.message}")
    # TODO: additional cross-field or runtime validations as needed


def test_new_form_schema(data=EXAMPLE_SCHEMA_V2):
    validate_new_form_schema(data)
    print("New form schema is valid")
